"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/remotion/CodeVideo.tsx":
/*!************************************!*\
  !*** ./src/remotion/CodeVideo.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CodeVideo: function() { return /* binding */ CodeVideo; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var remotion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! remotion */ \"(app-pages-browser)/./node_modules/remotion/dist/esm/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\nconst CodeVideo = (param)=>{\n    let { code, language, title = \"Generated Code\", theme = \"dark\" } = param;\n    _s();\n    const frame = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.useCurrentFrame)();\n    const { fps, durationInFrames } = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.useVideoConfig)();\n    // Animation timings\n    const titleDelay = 0;\n    const codeDelay = 30;\n    const lineDelay = 5;\n    // Title animation\n    const titleProgress = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.spring)({\n        frame: frame - titleDelay,\n        fps,\n        config: {\n            damping: 100,\n            stiffness: 200,\n            mass: 0.5\n        }\n    });\n    const titleOpacity = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.interpolate)(titleProgress, [\n        0,\n        1\n    ], [\n        0,\n        1\n    ]);\n    const titleScale = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.interpolate)(titleProgress, [\n        0,\n        1\n    ], [\n        0.8,\n        1\n    ]);\n    // Code lines animation\n    const codeLines = code.split(\"\\n\");\n    const totalLines = codeLines.length;\n    const getLineProgress = (lineIndex)=>{\n        const lineStartFrame = codeDelay + lineIndex * lineDelay;\n        return (0,remotion__WEBPACK_IMPORTED_MODULE_2__.spring)({\n            frame: frame - lineStartFrame,\n            fps,\n            config: {\n                damping: 100,\n                stiffness: 300,\n                mass: 0.3\n            }\n        });\n    };\n    // Background gradient animation\n    const backgroundProgress = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.interpolate)(frame, [\n        0,\n        durationInFrames\n    ], [\n        0,\n        1\n    ]);\n    const gradientRotation = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.interpolate)(backgroundProgress, [\n        0,\n        1\n    ], [\n        0,\n        360\n    ]);\n    // Syntax highlighting colors\n    const getTokenColor = (token, theme)=>{\n        const darkColors = {\n            keyword: \"#ff79c6\",\n            string: \"#f1fa8c\",\n            number: \"#bd93f9\",\n            comment: \"#6272a4\",\n            function: \"#50fa7b\",\n            variable: \"#8be9fd\",\n            default: \"#f8f8f2\"\n        };\n        const lightColors = {\n            keyword: \"#d73a49\",\n            string: \"#032f62\",\n            number: \"#005cc5\",\n            comment: \"#6a737d\",\n            function: \"#6f42c1\",\n            variable: \"#e36209\",\n            default: \"#24292e\"\n        };\n        const colors = theme === \"dark\" ? darkColors : lightColors;\n        // Simple token detection\n        if (/^(function|const|let|var|if|else|for|while|return|class|import|export)$/.test(token)) {\n            return colors.keyword;\n        }\n        if (/^['\"`].*['\"`]$/.test(token)) {\n            return colors.string;\n        }\n        if (/^\\d+$/.test(token)) {\n            return colors.number;\n        }\n        if (/^\\/\\//.test(token)) {\n            return colors.comment;\n        }\n        if (/^[a-zA-Z_$][a-zA-Z0-9_$]*\\s*\\(/.test(token)) {\n            return colors.function;\n        }\n        return colors.default;\n    };\n    const renderCodeLine = (line, lineIndex)=>{\n        const lineProgress = getLineProgress(lineIndex);\n        const lineOpacity = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.interpolate)(lineProgress, [\n            0,\n            1\n        ], [\n            0,\n            1\n        ]);\n        const lineTranslateX = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.interpolate)(lineProgress, [\n            0,\n            1\n        ], [\n            -50,\n            0\n        ]);\n        // Simple tokenization\n        const tokens = line.split(/(\\s+|[(){}[\\];,.])/);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center font-mono text-lg leading-relaxed\",\n            style: {\n                opacity: lineOpacity,\n                transform: \"translateX(\".concat(lineTranslateX, \"px)\")\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-gray-500 w-12 text-right mr-4 select-none\",\n                    children: lineIndex + 1\n                }, void 0, false, {\n                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: tokens.map((token, tokenIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                color: getTokenColor(token.trim(), theme)\n                            },\n                            children: token\n                        }, tokenIndex, false, {\n                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, lineIndex, true, {\n            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n            lineNumber: 128,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(remotion__WEBPACK_IMPORTED_MODULE_2__.AbsoluteFill, {\n        className: \"\".concat(theme === \"dark\" ? \"bg-gray-900\" : \"bg-gray-50\", \" flex flex-col items-center justify-center p-16\"),\n        style: {\n            background: theme === \"dark\" ? \"linear-gradient(\".concat(gradientRotation, \"deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)\") : \"linear-gradient(\".concat(gradientRotation, \"deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%)\")\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden\",\n                children: [\n                    ...Array(20)\n                ].map((_, i)=>{\n                    const delay = i * 10;\n                    const progress = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.spring)({\n                        frame: frame - delay,\n                        fps,\n                        config: {\n                            damping: 100,\n                            stiffness: 200\n                        }\n                    });\n                    const opacity = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.interpolate)(progress, [\n                        0,\n                        1\n                    ], [\n                        0,\n                        0.1\n                    ]);\n                    const scale = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.interpolate)(progress, [\n                        0,\n                        1\n                    ], [\n                        0,\n                        1\n                    ]);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute w-2 h-2 \".concat(theme === \"dark\" ? \"bg-blue-400\" : \"bg-blue-600\", \" rounded-full\"),\n                        style: {\n                            left: \"\".concat(i * 47 % 100, \"%\"),\n                            top: \"\".concat(i * 31 % 100, \"%\"),\n                            opacity,\n                            transform: \"scale(\".concat(scale, \")\")\n                        }\n                    }, i, false, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-12 text-center\",\n                style: {\n                    opacity: titleOpacity,\n                    transform: \"scale(\".concat(titleScale, \")\")\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-5xl font-bold mb-4 \".concat(theme === \"dark\" ? \"text-white\" : \"text-gray-900\"),\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xl \".concat(theme === \"dark\" ? \"text-blue-400\" : \"text-blue-600\", \" font-mono\"),\n                        children: language.toUpperCase()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\".concat(theme === \"dark\" ? \"bg-gray-800 border-gray-700\" : \"bg-white border-gray-200\", \" border-2 rounded-2xl p-8 shadow-2xl max-w-4xl w-full\"),\n                style: {\n                    backdropFilter: \"blur(10px)\",\n                    backgroundColor: theme === \"dark\" ? \"rgba(31, 41, 55, 0.9)\" : \"rgba(255, 255, 255, 0.9)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mb-6 pb-4 border-b border-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-yellow-500 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-green-500 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"ml-4 text-sm \".concat(theme === \"dark\" ? \"text-gray-400\" : \"text-gray-600\"),\n                                children: [\n                                    language,\n                                    \".\",\n                                    language === \"javascript\" ? \"js\" : language === \"python\" ? \"py\" : \"txt\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: codeLines.map((line, index)=>renderCodeLine(line, index))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 text-center \".concat(theme === \"dark\" ? \"text-gray-400\" : \"text-gray-600\"),\n                style: {\n                    opacity: (0,remotion__WEBPACK_IMPORTED_MODULE_2__.interpolate)(frame, [\n                        durationInFrames - 60,\n                        durationInFrames\n                    ], [\n                        0,\n                        1\n                    ])\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg\",\n                    children: \"Generated with Gemini 2.0 Flash & Remotion\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CodeVideo, \"nWTRM5yIlyccG/aXX9fSPeL5btc=\", false, function() {\n    return [\n        remotion__WEBPACK_IMPORTED_MODULE_2__.useCurrentFrame,\n        remotion__WEBPACK_IMPORTED_MODULE_2__.useVideoConfig\n    ];\n});\n_c = CodeVideo;\nvar _c;\n$RefreshReg$(_c, \"CodeVideo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/remotion/CodeVideo.tsx\n"));

/***/ })

});