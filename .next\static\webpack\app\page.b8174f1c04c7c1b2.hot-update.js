"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _remotion_player__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @remotion/player */ \"(app-pages-browser)/./node_modules/@remotion/player/dist/esm/index.mjs\");\n/* harmony import */ var _remotion_CodeVideo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/remotion/CodeVideo */ \"(app-pages-browser)/./src/remotion/CodeVideo.tsx\");\n/* harmony import */ var _hooks_useChat__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useChat */ \"(app-pages-browser)/./src/hooks/useChat.ts\");\n/* harmony import */ var _components_ChatInterface__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ChatInterface */ \"(app-pages-browser)/./src/components/ChatInterface.tsx\");\n/* harmony import */ var _components_VideoControls__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/VideoControls */ \"(app-pages-browser)/./src/components/VideoControls.tsx\");\n/* harmony import */ var _barrel_optimize_names_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst defaultVideoData = {\n    code: \"function fibonacci(n) {\\n  if (n <= 1) return n;\\n  return fibonacci(n - 1) + fibonacci(n - 2);\\n}\\n\\nconsole.log(fibonacci(10)); // Output: 55\",\n    language: \"javascript\",\n    title: \"Fibonacci Function\",\n    theme: \"dark\"\n};\nfunction Home() {\n    _s();\n    const [videoData, setVideoData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultVideoData);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentFrame, setCurrentFrame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { messages, isLoading, error, sendMessage, generateCodeFromPrompt, clearMessages, clearError } = (0,_hooks_useChat__WEBPACK_IMPORTED_MODULE_3__.useChat)();\n    const videoConfig = {\n        fps: 30,\n        durationInFrames: 300,\n        width: 1920,\n        height: 1080\n    };\n    const handleCodeGenerated = (codeResponse)=>{\n        setVideoData({\n            code: codeResponse.code,\n            language: codeResponse.language,\n            title: codeResponse.title || \"Generated Code\",\n            theme: \"dark\"\n        });\n    };\n    const handlePlayPause = ()=>{\n        setIsPlaying(!isPlaying);\n    };\n    const handleRestart = ()=>{\n        setCurrentFrame(0);\n        setIsPlaying(true);\n    };\n    const handleThemeToggle = ()=>{\n        setVideoData((prev)=>({\n                ...prev,\n                theme: prev.theme === \"dark\" ? \"light\" : \"dark\"\n            }));\n    };\n    const handleLanguageChange = (language)=>{\n        setVideoData((prev)=>({\n                ...prev,\n                language\n            }));\n    };\n    const handleExportVideo = ()=>{\n        // This would typically trigger a server-side render\n        alert(\"Video export functionality would be implemented with a backend service\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-center bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent\",\n                            children: \"Remotion + Gemini Code Video Generator\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-gray-400 mt-2\",\n                            children: \"Generate animated code videos using Gemini 2.0 Flash and Remotion\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 rounded-xl p-6 shadow-2xl border border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-2xl font-semibold mb-4 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"w-3 h-3 bg-green-500 rounded-full mr-3 animate-pulse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 96,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Chat with Gemini\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInterface__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        messages: messages,\n                                        isLoading: isLoading,\n                                        error: error,\n                                        onSendMessage: sendMessage,\n                                        onCodeGenerated: handleCodeGenerated,\n                                        onClearMessages: clearMessages,\n                                        onClearError: clearError\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 rounded-xl p-6 shadow-2xl border border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Quick Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 112,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>generateCodeFromPrompt(\"Create a React component for a todo list\"),\n                                                className: \"btn-primary text-sm\",\n                                                disabled: isLoading,\n                                                children: \"React Todo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>generateCodeFromPrompt(\"Write a Python function to sort an array\"),\n                                                className: \"btn-primary text-sm\",\n                                                disabled: isLoading,\n                                                children: \"Python Sort\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>generateCodeFromPrompt(\"Create a JavaScript API client\"),\n                                                className: \"btn-primary text-sm\",\n                                                disabled: isLoading,\n                                                children: \"JS API Client\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>generateCodeFromPrompt(\"Write a CSS animation for a loading spinner\"),\n                                                className: \"btn-primary text-sm\",\n                                                disabled: isLoading,\n                                                children: \"CSS Animation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 rounded-xl p-6 shadow-2xl border border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-semibold\",\n                                                children: \"Video Preview\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowSettings(!showSettings),\n                                                className: \"p-2 hover:bg-gray-700 rounded-lg transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"video-container mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_remotion_player__WEBPACK_IMPORTED_MODULE_7__.Player, {\n                                            component: _remotion_CodeVideo__WEBPACK_IMPORTED_MODULE_2__.CodeVideo,\n                                            inputProps: videoData,\n                                            durationInFrames: videoConfig.durationInFrames,\n                                            fps: videoConfig.fps,\n                                            compositionWidth: videoConfig.width,\n                                            compositionHeight: videoConfig.height,\n                                            style: {\n                                                width: \"100%\",\n                                                aspectRatio: \"16/9\"\n                                            },\n                                            controls: false,\n                                            loop: true,\n                                            autoPlay: isPlaying\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoControls__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        isPlaying: isPlaying,\n                                        onPlayPause: handlePlayPause,\n                                        onRestart: handleRestart,\n                                        onExport: handleExportVideo,\n                                        currentFrame: currentFrame,\n                                        totalFrames: videoConfig.durationInFrames,\n                                        fps: videoConfig.fps\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this),\n                                    showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 p-4 bg-gray-700 rounded-lg border border-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold mb-3\",\n                                                children: \"Video Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-1\",\n                                                                children: \"Theme\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 195,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleThemeToggle,\n                                                                className: \"px-3 py-1 rounded text-sm \".concat(videoData.theme === \"dark\" ? \"bg-gray-900 text-white\" : \"bg-gray-100 text-gray-900\"),\n                                                                children: videoData.theme === \"dark\" ? \"Dark\" : \"Light\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-1\",\n                                                                children: \"Language\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: videoData.language,\n                                                                onChange: (e)=>handleLanguageChange(e.target.value),\n                                                                className: \"bg-gray-600 border border-gray-500 rounded px-3 py-1 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"javascript\",\n                                                                        children: \"JavaScript\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 214,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"python\",\n                                                                        children: \"Python\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 215,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"typescript\",\n                                                                        children: \"TypeScript\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 216,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"java\",\n                                                                        children: \"Java\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 217,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"cpp\",\n                                                                        children: \"C++\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 218,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"rust\",\n                                                                        children: \"Rust\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 219,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"go\",\n                                                                        children: \"Go\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 220,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-1\",\n                                                                children: \"Title\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 224,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: videoData.title,\n                                                                onChange: (e)=>setVideoData((prev)=>({\n                                                                            ...prev,\n                                                                            title: e.target.value\n                                                                        })),\n                                                                className: \"bg-gray-600 border border-gray-500 rounded px-3 py-1 text-sm w-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 223,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 rounded-xl p-6 shadow-2xl border border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Current Code\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 rounded-lg p-4 font-mono text-sm overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"text-gray-300 whitespace-pre-wrap\",\n                                            children: videoData.code\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3 flex items-center justify-between text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Language: \",\n                                                    videoData.language\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Lines: \",\n                                                    videoData.code.split(\"\\n\").length\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"mt-16 text-center text-gray-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Built with Next.js, Remotion, and Gemini 2.0 Flash\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 253,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"WvFhVIoWe/b2RKZ7OfqM49gO+Lc=\", false, function() {\n    return [\n        _hooks_useChat__WEBPACK_IMPORTED_MODULE_3__.useChat\n    ];\n});\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});