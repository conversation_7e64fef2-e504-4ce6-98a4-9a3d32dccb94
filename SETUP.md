# Setup Instructions

## Prerequisites

1. **Node.js**: Install Node.js 18.0.0 or higher from [nodejs.org](https://nodejs.org/)
2. **Gemini API Key**: Get your free API key from [Google AI Studio](https://makersuite.google.com/app/apikey)

## Installation Steps

### 1. Install Dependencies

```bash
npm install
```

### 2. Environment Configuration

```bash
# Copy the environment template
npm run setup

# Edit .env.local with your actual API key
# Replace 'AIzaSyDNx1rMll9c-OUlAglZuHJDici3TrY9i3w' with your actual Gemini API key
```

### 3. Verify Installation

```bash
# Check if everything is working
npm run type-check
npm run lint
```

### 4. Start Development

```bash
# Start the Next.js development server
npm run dev
```

The application will be available at [http://localhost:3000](http://localhost:3000)

### 5. Optional: Remotion Studio

```bash
# Start Remotion Studio for video editing (optional)
npm run remotion:dev
```

Remotion Studio will be available at [http://localhost:3001](http://localhost:3001)

## Troubleshooting

### Common Issues

1. **"Module not found" errors**
   ```bash
   npm run clean
   npm install
   ```

2. **TypeScript errors**
   ```bash
   npm run type-check
   ```

3. **Gemini API errors**
   - Verify your API key in `.env.local`
   - Check API key permissions at [Google AI Studio](https://makersuite.google.com/app/apikey)

4. **Remotion rendering issues**
   ```bash
   npm run remotion:versions
   npm run remotion:upgrade
   ```

### Environment Variables

Required:
- `NEXT_PUBLIC_GEMINI_API_KEY`: Your Gemini API key

Optional:
- `REMOTION_LICENSE_KEY`: For commercial Remotion usage
- `REMOTION_STUDIO_PORT`: Custom port for Remotion Studio (default: 3001)

## Development Workflow

1. **Start development server**: `npm run dev`
2. **Make changes** to the code
3. **Test in browser** at localhost:3000
4. **Generate videos** using the chat interface
5. **Preview videos** with Remotion Player

## Production Deployment

### Vercel (Recommended)

1. Install Vercel CLI: `npm i -g vercel`
2. Run: `vercel`
3. Add environment variables in Vercel dashboard
4. Deploy: `vercel --prod`

### Other Platforms

The app works on any Next.js-compatible platform:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## Next Steps

1. **Customize video settings** in `remotion.config.ts`
2. **Modify themes** in `tailwind.config.js`
3. **Add new languages** in `src/lib/gemini.ts`
4. **Extend chat features** in `src/hooks/useChat.ts`

## Getting Help

- Check the main [README.md](README.md) for detailed documentation
- Review the [troubleshooting section](#troubleshooting) above
- Create an issue if you encounter problems
