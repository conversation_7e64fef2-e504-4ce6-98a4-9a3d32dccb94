import { Config } from '@remotion/cli/config';

Config.setVideoImageFormat('jpeg');
Config.setOverwriteOutput(true);
Config.setPixelFormat('yuv420p');
Config.setCodec('h264');

// Set the entry point for Remotion
Config.setEntryPoint('./src/remotion/index.ts');

// Configure output settings
Config.setOutputLocation('./out/video.mp4');

// Enable concurrent rendering for better performance
Config.setConcurrency(4);
