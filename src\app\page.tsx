'use client';

import React, { useState, useEffect } from 'react';
import { Player } from '@remotion/player';
import { CodeVideo, CodeVideoProps } from '@/remotion/CodeVideo';
import { useChat } from '@/hooks/useChat';
import { CodeGenerationResponse } from '@/lib/gemini';
import { CodeVideoData } from '@/lib/types';
import ChatInterface from '@/components/ChatInterface';
import VideoControls from '@/components/VideoControls';
import CodeToVideo from '@/components/CodeToVideo';
import { Play, Pause, RotateCcw, Download, Settings, MessageSquare, Code2 } from 'lucide-react';

const defaultVideoData: CodeVideoData = {
  code: `function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

console.log(fibonacci(10)); // Output: 55`,
  language: 'javascript',
  title: '<PERSON>bon<PERSON>ci Function',
  theme: 'dark',
};

export default function Home() {
  const [videoData, setVideoData] = useState<CodeVideoData>(defaultVideoData);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentFrame, setCurrentFrame] = useState(0);
  const [showSettings, setShowSettings] = useState(false);
  const [activeTab, setActiveTab] = useState<'chat' | 'code'>('chat');
  const [isGeneratingVideo, setIsGeneratingVideo] = useState(false);
  const { messages, isLoading, error, sendMessage, generateCodeFromPrompt, clearMessages, clearError } = useChat();

  const videoConfig = {
    fps: 30,
    durationInFrames: 300,
    width: 1920,
    height: 1080,
  };

  const handleCodeGenerated = (codeResponse: CodeGenerationResponse) => {
    setVideoData({
      code: codeResponse.code,
      language: codeResponse.language,
      title: codeResponse.title || 'Generated Code',
      theme: 'dark',
    });
  };

  const handleDirectCodeToVideo = (codeResponse: CodeGenerationResponse) => {
    setIsGeneratingVideo(true);

    // Simulate processing time for better UX
    setTimeout(() => {
      setVideoData({
        code: codeResponse.code,
        language: codeResponse.language,
        title: codeResponse.title || 'Generated Code',
        theme: 'dark',
      });
      setIsGeneratingVideo(false);

      // Auto-play the video
      setIsPlaying(true);
      setCurrentFrame(0);
    }, 1000);
  };

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handleRestart = () => {
    setCurrentFrame(0);
    setIsPlaying(true);
  };

  const handleThemeToggle = () => {
    setVideoData(prev => ({
      ...prev,
      theme: prev.theme === 'dark' ? 'light' : 'dark',
    }));
  };

  const handleLanguageChange = (language: string) => {
    setVideoData(prev => ({
      ...prev,
      language,
    }));
  };

  const handleExportVideo = () => {
    // This would typically trigger a server-side render
    alert('Video export functionality would be implemented with a backend service');
  };

  return (
    <div className="min-h-screen p-4">
      {/* Header */}
      <header className="mb-8">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-4xl font-bold text-center bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
            Remotion + Gemini Code Video Generator
          </h1>
          <p className="text-center text-gray-400 mt-2">
            Generate animated code videos using Gemini 2.0 Flash and Remotion
          </p>
        </div>
      </header>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Panel - Chat/Code Input */}
        <div className="space-y-6">
          {/* Tab Navigation */}
          <div className="bg-gray-800 rounded-xl shadow-2xl border border-gray-700 overflow-hidden">
            <div className="flex border-b border-gray-700">
              <button
                onClick={() => setActiveTab('chat')}
                className={`flex-1 px-6 py-4 text-sm font-medium transition-colors ${
                  activeTab === 'chat'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                }`}
              >
                <MessageSquare className="w-4 h-4 inline mr-2" />
                Chat with AI
              </button>
              <button
                onClick={() => setActiveTab('code')}
                className={`flex-1 px-6 py-4 text-sm font-medium transition-colors ${
                  activeTab === 'code'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                }`}
              >
                <Code2 className="w-4 h-4 inline mr-2" />
                Code to Video
              </button>
            </div>

            <div className="p-6">
              {activeTab === 'chat' ? (
                <div>
                  <h2 className="text-2xl font-semibold mb-4 flex items-center">
                    <span className="w-3 h-3 bg-green-500 rounded-full mr-3 animate-pulse"></span>
                    Chat with Gemini
                  </h2>
                  <ChatInterface
                    messages={messages}
                    isLoading={isLoading}
                    error={error}
                    onSendMessage={sendMessage}
                    onCodeGenerated={handleCodeGenerated}
                    onClearMessages={clearMessages}
                    onClearError={clearError}
                  />
                </div>
              ) : (
                <CodeToVideo
                  onGenerateVideo={handleDirectCodeToVideo}
                  isGenerating={isGeneratingVideo}
                />
              )}
            </div>
          </div>

          {/* Quick Actions - Only show in chat mode */}
          {activeTab === 'chat' && (
            <div className="bg-gray-800 rounded-xl p-6 shadow-2xl border border-gray-700">
              <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={() => generateCodeFromPrompt('Create a React component for a todo list')}
                  className="btn-primary text-sm"
                  disabled={isLoading}
                >
                  React Todo
                </button>
                <button
                  onClick={() => generateCodeFromPrompt('Write a Python function to sort an array')}
                  className="btn-primary text-sm"
                  disabled={isLoading}
                >
                  Python Sort
                </button>
                <button
                  onClick={() => generateCodeFromPrompt('Create a JavaScript API client')}
                  className="btn-primary text-sm"
                  disabled={isLoading}
                >
                  JS API Client
                </button>
                <button
                  onClick={() => generateCodeFromPrompt('Write a CSS animation for a loading spinner')}
                  className="btn-primary text-sm"
                  disabled={isLoading}
                >
                  CSS Animation
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Video Preview */}
        <div className="space-y-6">
          <div className="bg-gray-800 rounded-xl p-6 shadow-2xl border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-2xl font-semibold flex items-center">
                Video Preview
                {isGeneratingVideo && (
                  <span className="ml-3 flex items-center text-sm text-blue-400">
                    <div className="spinner mr-2"></div>
                    Generating...
                  </span>
                )}
              </h2>
              <button
                onClick={() => setShowSettings(!showSettings)}
                className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
              >
                <Settings className="w-5 h-5" />
              </button>
            </div>

            {/* Video Player */}
            <div className="video-container mb-4">
              <Player
                component={CodeVideo}
                inputProps={videoData}
                durationInFrames={videoConfig.durationInFrames}
                fps={videoConfig.fps}
                compositionWidth={videoConfig.width}
                compositionHeight={videoConfig.height}
                style={{
                  width: '100%',
                  aspectRatio: '16/9',
                }}
                controls={false}
                loop
                autoPlay={isPlaying}
                acknowledgeRemotionLicense
              />
            </div>

            {/* Video Controls */}
            <VideoControls
              isPlaying={isPlaying}
              onPlayPause={handlePlayPause}
              onRestart={handleRestart}
              onExport={handleExportVideo}
              currentFrame={currentFrame}
              totalFrames={videoConfig.durationInFrames}
              fps={videoConfig.fps}
            />

            {/* Settings Panel */}
            {showSettings && (
              <div className="mt-4 p-4 bg-gray-700 rounded-lg border border-gray-600">
                <h4 className="font-semibold mb-3">Video Settings</h4>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium mb-1">Theme</label>
                    <button
                      onClick={handleThemeToggle}
                      className={`px-3 py-1 rounded text-sm ${
                        videoData.theme === 'dark' 
                          ? 'bg-gray-900 text-white' 
                          : 'bg-gray-100 text-gray-900'
                      }`}
                    >
                      {videoData.theme === 'dark' ? 'Dark' : 'Light'}
                    </button>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Language</label>
                    <select
                      value={videoData.language}
                      onChange={(e) => handleLanguageChange(e.target.value)}
                      className="bg-gray-600 border border-gray-500 rounded px-3 py-1 text-sm"
                    >
                      <option value="javascript">JavaScript</option>
                      <option value="python">Python</option>
                      <option value="typescript">TypeScript</option>
                      <option value="java">Java</option>
                      <option value="cpp">C++</option>
                      <option value="rust">Rust</option>
                      <option value="go">Go</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-1">Title</label>
                    <input
                      type="text"
                      value={videoData.title}
                      onChange={(e) => setVideoData(prev => ({ ...prev, title: e.target.value }))}
                      className="bg-gray-600 border border-gray-500 rounded px-3 py-1 text-sm w-full"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Code Display */}
          <div className="bg-gray-800 rounded-xl p-6 shadow-2xl border border-gray-700">
            <h3 className="text-lg font-semibold mb-4">Current Code</h3>
            <div className="bg-gray-900 rounded-lg p-4 font-mono text-sm overflow-x-auto">
              <pre className="text-gray-300 whitespace-pre-wrap">{videoData.code}</pre>
            </div>
            <div className="mt-3 flex items-center justify-between text-sm text-gray-400">
              <span>Language: {videoData.language}</span>
              <span>Lines: {videoData.code.split('\n').length}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="mt-16 text-center text-gray-500">
        <p>Built with Next.js, Remotion, and Gemini 2.0 Flash</p>
      </footer>
    </div>
  );
}
