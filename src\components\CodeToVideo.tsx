'use client';

import React, { useState } from 'react';
import { Play, Copy, Download, Settings, Code2, Wand2 } from 'lucide-react';
import { CodeGenerationResponse, detectLanguage } from '@/lib/gemini';

interface CodeToVideoProps {
  onGenerateVideo: (codeData: CodeGenerationResponse) => void;
  isGenerating?: boolean;
}

const CodeToVideo: React.FC<CodeToVideoProps> = ({ onGenerateVideo, isGenerating = false }) => {
  const [code, setCode] = useState('');
  const [language, setLanguage] = useState('javascript');
  const [title, setTitle] = useState('');
  const [theme, setTheme] = useState<'dark' | 'light'>('dark');
  const [autoDetectLanguage, setAutoDetectLanguage] = useState(true);

  const supportedLanguages = [
    { value: 'javascript', label: 'JavaScript' },
    { value: 'typescript', label: 'TypeScript' },
    { value: 'python', label: 'Python' },
    { value: 'java', label: 'Java' },
    { value: 'cpp', label: 'C++' },
    { value: 'rust', label: 'Rust' },
    { value: 'go', label: 'Go' },
    { value: 'html', label: 'HTML' },
    { value: 'css', label: 'CSS' },
    { value: 'sql', label: 'SQL' },
    { value: 'bash', label: 'Bash' },
    { value: 'json', label: 'JSON' },
  ];

  const exampleCodes = {
    javascript: `function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fibonacci(n - 2);
}

console.log(fibonacci(10)); // Output: 55`,
    python: `def quicksort(arr):
    if len(arr) <= 1:
        return arr
    
    pivot = arr[len(arr) // 2]
    left = [x for x in arr if x < pivot]
    middle = [x for x in arr if x == pivot]
    right = [x for x in arr if x > pivot]
    
    return quicksort(left) + middle + quicksort(right)

print(quicksort([3, 6, 8, 10, 1, 2, 1]))`,
    react: `import React, { useState } from 'react';

function TodoApp() {
  const [todos, setTodos] = useState([]);
  const [input, setInput] = useState('');

  const addTodo = () => {
    if (input.trim()) {
      setTodos([...todos, { id: Date.now(), text: input, done: false }]);
      setInput('');
    }
  };

  return (
    <div className="todo-app">
      <h1>Todo List</h1>
      <div>
        <input 
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="Add a todo..."
        />
        <button onClick={addTodo}>Add</button>
      </div>
      <ul>
        {todos.map(todo => (
          <li key={todo.id}>{todo.text}</li>
        ))}
      </ul>
    </div>
  );
}`,
  };

  const handleCodeChange = (newCode: string) => {
    setCode(newCode);
    
    if (autoDetectLanguage && newCode.trim()) {
      const detectedLang = detectLanguage(newCode);
      setLanguage(detectedLang);
    }
  };

  const handleGenerateVideo = () => {
    if (!code.trim()) {
      alert('Please enter some code first!');
      return;
    }

    const videoTitle = title.trim() || `${language.charAt(0).toUpperCase() + language.slice(1)} Code`;
    
    const codeData: CodeGenerationResponse = {
      code: code.trim(),
      language,
      title: videoTitle,
      explanation: `Generated video for ${language} code`,
    };

    onGenerateVideo(codeData);
  };

  const loadExample = (exampleType: keyof typeof exampleCodes) => {
    setCode(exampleCodes[exampleType]);
    setLanguage(exampleType === 'react' ? 'javascript' : exampleType);
    setTitle(exampleType === 'react' ? 'React Todo App' : `${exampleType.charAt(0).toUpperCase() + exampleType.slice(1)} Example`);
  };

  const pasteFromClipboard = async () => {
    try {
      const text = await navigator.clipboard.readText();
      handleCodeChange(text);
    } catch (err) {
      console.error('Failed to read clipboard:', err);
      alert('Failed to read from clipboard. Please paste manually.');
    }
  };

  const clearCode = () => {
    setCode('');
    setTitle('');
  };

  return (
    <div className="bg-gray-800 rounded-xl p-6 shadow-2xl border border-gray-700">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-semibold flex items-center">
          <Code2 className="w-6 h-6 mr-3 text-blue-400" />
          Code to Video Generator
        </h2>
        <div className="flex items-center space-x-2">
          <button
            onClick={pasteFromClipboard}
            className="px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded transition-colors"
            title="Paste from clipboard"
          >
            <Copy className="w-4 h-4" />
          </button>
          <button
            onClick={clearCode}
            className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors"
            disabled={!code}
          >
            Clear
          </button>
        </div>
      </div>

      {/* Example Code Buttons */}
      <div className="mb-4">
        <p className="text-sm text-gray-400 mb-2">Quick Examples:</p>
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => loadExample('javascript')}
            className="px-3 py-1 bg-yellow-600 hover:bg-yellow-700 text-white text-xs rounded transition-colors"
          >
            Fibonacci (JS)
          </button>
          <button
            onClick={() => loadExample('python')}
            className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors"
          >
            Quicksort (Python)
          </button>
          <button
            onClick={() => loadExample('react')}
            className="px-3 py-1 bg-cyan-600 hover:bg-cyan-700 text-white text-xs rounded transition-colors"
          >
            Todo App (React)
          </button>
        </div>
      </div>

      {/* Code Input */}
      <div className="mb-4">
        <label className="block text-sm font-medium mb-2 text-gray-300">
          Your Code
        </label>
        <textarea
          value={code}
          onChange={(e) => handleCodeChange(e.target.value)}
          placeholder="Paste your code here or use the examples above..."
          className="w-full h-64 p-4 bg-gray-900 border border-gray-600 rounded-lg font-mono text-sm text-gray-100 placeholder-gray-500 resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          spellCheck={false}
        />
        <div className="flex justify-between items-center mt-2 text-xs text-gray-500">
          <span>Lines: {code.split('\n').length}</span>
          <span>Characters: {code.length}</span>
        </div>
      </div>

      {/* Settings Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Language Selection */}
        <div>
          <label className="block text-sm font-medium mb-1 text-gray-300">
            Language
          </label>
          <select
            value={language}
            onChange={(e) => setLanguage(e.target.value)}
            className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-sm text-gray-100"
          >
            {supportedLanguages.map((lang) => (
              <option key={lang.value} value={lang.value}>
                {lang.label}
              </option>
            ))}
          </select>
          <div className="flex items-center mt-1">
            <input
              type="checkbox"
              id="autoDetect"
              checked={autoDetectLanguage}
              onChange={(e) => setAutoDetectLanguage(e.target.checked)}
              className="mr-1"
            />
            <label htmlFor="autoDetect" className="text-xs text-gray-400">
              Auto-detect
            </label>
          </div>
        </div>

        {/* Title */}
        <div>
          <label className="block text-sm font-medium mb-1 text-gray-300">
            Video Title
          </label>
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Enter video title..."
            className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-sm text-gray-100 placeholder-gray-500"
          />
        </div>

        {/* Theme */}
        <div>
          <label className="block text-sm font-medium mb-1 text-gray-300">
            Theme
          </label>
          <select
            value={theme}
            onChange={(e) => setTheme(e.target.value as 'dark' | 'light')}
            className="w-full p-2 bg-gray-700 border border-gray-600 rounded text-sm text-gray-100"
          >
            <option value="dark">Dark</option>
            <option value="light">Light</option>
          </select>
        </div>

        {/* Generate Button */}
        <div className="flex items-end">
          <button
            onClick={handleGenerateVideo}
            disabled={!code.trim() || isGenerating}
            className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-600 disabled:to-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-all duration-200 font-medium"
          >
            {isGenerating ? (
              <>
                <div className="spinner"></div>
                <span>Generating...</span>
              </>
            ) : (
              <>
                <Wand2 className="w-4 h-4" />
                <span>Generate Video</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Info */}
      <div className="bg-gray-700 rounded-lg p-3 text-sm text-gray-300">
        <p className="flex items-center">
          <Settings className="w-4 h-4 mr-2 text-blue-400" />
          <strong>Tip:</strong> The video will be 10 seconds long at 1920x1080 resolution. 
          Code will animate line by line with syntax highlighting.
        </p>
      </div>
    </div>
  );
};

export default CodeToVideo;
