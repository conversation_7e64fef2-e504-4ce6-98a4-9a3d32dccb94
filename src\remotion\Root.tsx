import React from 'react';
import { Composition } from 'remotion';
import { CodeVideo, CodeVideoProps } from './CodeVideo';

export const Root: React.FC = () => {
  return (
    <>
      <Composition
        id="CodeVideo"
        component={CodeVideo}
        durationInFrames={300}
        fps={30}
        width={1920}
        height={1080}
        defaultProps={{
          code: `function fibonacci(n) {
  if (n <= 1) return n;
  return fibonacci(n - 1) + fi<PERSON><PERSON>ci(n - 2);
}

console.log(fibonacci(10));`,
          language: 'javascript',
          title: 'Fibonacci Function',
          theme: 'dark' as const,
        }}
      />
    </>
  );
};
