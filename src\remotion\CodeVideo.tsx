import React from 'react';
import {
  AbsoluteFill,
  interpolate,
  spring,
  useCurrentFrame,
  useVideoConfig,
} from 'remotion';

export interface CodeVideoProps extends Record<string, unknown> {
  code: string;
  language: string;
  title?: string;
  theme?: 'dark' | 'light';
}

export const CodeVideo: React.FC<CodeVideoProps> = ({
  code,
  language,
  title = 'Generated Code',
  theme = 'dark',
}) => {
  const frame = useCurrentFrame();
  const { fps, durationInFrames } = useVideoConfig();

  // Animation timings
  const titleDelay = 0;
  const codeDelay = 30;
  const lineDelay = 5;

  // Title animation
  const titleProgress = spring({
    frame: frame - titleDelay,
    fps,
    config: {
      damping: 100,
      stiffness: 200,
      mass: 0.5,
    },
  });

  const titleOpacity = interpolate(titleProgress, [0, 1], [0, 1]);
  const titleScale = interpolate(titleProgress, [0, 1], [0.8, 1]);

  // Code lines animation
  const codeLines = code.split('\n');
  const totalLines = codeLines.length;

  const getLineProgress = (lineIndex: number) => {
    const lineStartFrame = codeDelay + lineIndex * lineDelay;
    return spring({
      frame: frame - lineStartFrame,
      fps,
      config: {
        damping: 100,
        stiffness: 300,
        mass: 0.3,
      },
    });
  };

  // Background gradient animation
  const backgroundProgress = interpolate(
    frame,
    [0, durationInFrames],
    [0, 1]
  );

  const gradientRotation = interpolate(
    backgroundProgress,
    [0, 1],
    [0, 360]
  );

  // Syntax highlighting colors
  const getTokenColor = (token: string, theme: 'dark' | 'light') => {
    const darkColors = {
      keyword: '#ff79c6',
      string: '#f1fa8c',
      number: '#bd93f9',
      comment: '#6272a4',
      function: '#50fa7b',
      variable: '#8be9fd',
      default: '#f8f8f2',
    };

    const lightColors = {
      keyword: '#d73a49',
      string: '#032f62',
      number: '#005cc5',
      comment: '#6a737d',
      function: '#6f42c1',
      variable: '#e36209',
      default: '#24292e',
    };

    const colors = theme === 'dark' ? darkColors : lightColors;

    // Simple token detection
    if (/^(function|const|let|var|if|else|for|while|return|class|import|export)$/.test(token)) {
      return colors.keyword;
    }
    if (/^['"`].*['"`]$/.test(token)) {
      return colors.string;
    }
    if (/^\d+$/.test(token)) {
      return colors.number;
    }
    if (/^\/\//.test(token)) {
      return colors.comment;
    }
    if (/^[a-zA-Z_$][a-zA-Z0-9_$]*\s*\(/.test(token)) {
      return colors.function;
    }

    return colors.default;
  };

  const renderCodeLine = (line: string, lineIndex: number) => {
    const lineProgress = getLineProgress(lineIndex);
    const lineOpacity = interpolate(lineProgress, [0, 1], [0, 1]);
    const lineTranslateX = interpolate(lineProgress, [0, 1], [-50, 0]);

    // Simple tokenization
    const tokens = line.split(/(\s+|[(){}[\];,.])/);

    return (
      <div
        key={lineIndex}
        className="flex items-center font-mono text-lg leading-relaxed"
        style={{
          opacity: lineOpacity,
          transform: `translateX(${lineTranslateX}px)`,
        }}
      >
        <span className="text-gray-500 w-12 text-right mr-4 select-none">
          {lineIndex + 1}
        </span>
        <div className="flex-1">
          {tokens.map((token, tokenIndex) => (
            <span
              key={tokenIndex}
              style={{
                color: getTokenColor(token.trim(), theme),
              }}
            >
              {token}
            </span>
          ))}
        </div>
      </div>
    );
  };

  return (
    <AbsoluteFill
      className={`${
        theme === 'dark' ? 'bg-gray-900' : 'bg-gray-50'
      } flex flex-col items-center justify-center p-16`}
      style={{
        background: theme === 'dark' 
          ? `linear-gradient(${gradientRotation}deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)`
          : `linear-gradient(${gradientRotation}deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%)`,
      }}
    >
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => {
          const delay = i * 10;
          const progress = spring({
            frame: frame - delay,
            fps,
            config: { damping: 100, stiffness: 200 },
          });
          const opacity = interpolate(progress, [0, 1], [0, 0.1]);
          const scale = interpolate(progress, [0, 1], [0, 1]);
          
          return (
            <div
              key={i}
              className={`absolute w-2 h-2 ${
                theme === 'dark' ? 'bg-blue-400' : 'bg-blue-600'
              } rounded-full`}
              style={{
                left: `${(i * 47) % 100}%`,
                top: `${(i * 31) % 100}%`,
                opacity,
                transform: `scale(${scale})`,
              }}
            />
          );
        })}
      </div>

      {/* Title */}
      <div
        className="mb-12 text-center"
        style={{
          opacity: titleOpacity,
          transform: `scale(${titleScale})`,
        }}
      >
        <h1
          className={`text-5xl font-bold mb-4 ${
            theme === 'dark' ? 'text-white' : 'text-gray-900'
          }`}
        >
          {title}
        </h1>
        <div
          className={`text-xl ${
            theme === 'dark' ? 'text-blue-400' : 'text-blue-600'
          } font-mono`}
        >
          {language.toUpperCase()}
        </div>
      </div>

      {/* Code container */}
      <div
        className={`${
          theme === 'dark' 
            ? 'bg-gray-800 border-gray-700' 
            : 'bg-white border-gray-200'
        } border-2 rounded-2xl p-8 shadow-2xl max-w-4xl w-full`}
        style={{
          backdropFilter: 'blur(10px)',
          backgroundColor: theme === 'dark' 
            ? 'rgba(31, 41, 55, 0.9)' 
            : 'rgba(255, 255, 255, 0.9)',
        }}
      >
        {/* Code header */}
        <div className="flex items-center mb-6 pb-4 border-b border-gray-600">
          <div className="flex space-x-2">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          </div>
          <div
            className={`ml-4 text-sm ${
              theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
            }`}
          >
            {language}.{language === 'javascript' ? 'js' : language === 'python' ? 'py' : 'txt'}
          </div>
        </div>

        {/* Code content */}
        <div className="space-y-2">
          {codeLines.map((line, index) => renderCodeLine(line, index))}
        </div>
      </div>

      {/* Footer */}
      <div
        className={`mt-8 text-center ${
          theme === 'dark' ? 'text-gray-400' : 'text-gray-600'
        }`}
        style={{
          opacity: interpolate(frame, [durationInFrames - 60, durationInFrames], [0, 1]),
        }}
      >
        <p className="text-lg">Generated with Gemini 2.0 Flash & Remotion</p>
      </div>
    </AbsoluteFill>
  );
};
