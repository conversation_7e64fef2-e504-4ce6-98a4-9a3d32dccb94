export interface VideoSettings {
  fps: number;
  durationInFrames: number;
  width: number;
  height: number;
}

export interface CodeVideoData extends Record<string, unknown> {
  code: string;
  language: string;
  title: string;
  theme: 'dark' | 'light';
}

export interface RenderProgress {
  progress: number;
  isRendering: boolean;
  error?: string;
}

export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

export interface ApiResponse<T> {
  data?: T;
  error?: string;
  loading: boolean;
}
