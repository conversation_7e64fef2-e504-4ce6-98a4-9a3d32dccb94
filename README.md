# Remotion + Gemini Code Video Generator

A powerful web application that combines **Gemini 2.0 Flash API** for intelligent code generation with **Remotion 4.0** for creating stunning animated code videos. Features a real-time chat interface and instant video preview capabilities.

## ✨ Features

- 🤖 **AI-Powered Code Generation** - Chat with Gemini 2.0 Flash to generate code in multiple languages
- 🎬 **Animated Code Videos** - Create beautiful animated presentations of your code using Remotion
- 💬 **Real-time Chat Interface** - Interactive conversation with AI for code assistance
- 🎮 **Live Video Preview** - Instant preview of generated videos with Remotion Player
- 🎨 **Customizable Themes** - Dark and light themes for video output
- 📱 **Responsive Design** - Works seamlessly on desktop and mobile devices
- 🚀 **Multiple Languages** - Support for JavaScript, Python, TypeScript, Java, C++, Rust, and Go

## 🛠️ Tech Stack

- **Frontend**: Next.js 14, TypeScript, Tailwind CSS
- **Video Generation**: Remotion 4.0
- **AI Integration**: Google Generative AI (Gemini 2.0 Flash)
- **UI Components**: Lucide React icons
- **Styling**: Tailwind CSS with custom animations

## 📋 Prerequisites

- Node.js 18.0.0 or higher
- npm or yarn package manager
- Gemini API key from [Google AI Studio](https://makersuite.google.com/app/apikey)

## 🚀 Quick Start

### 1. Clone and Install

```bash
# Clone the repository
git clone <your-repo-url>
cd remotion-gemini-video-generator

# Install dependencies
npm install
```

### 2. Environment Setup

```bash
# Copy the environment template
cp .env.local.template .env.local

# Edit .env.local and add your Gemini API key
NEXT_PUBLIC_GEMINI_API_KEY=your_actual_api_key_here
```

### 3. Development

```bash
# Start the Next.js development server
npm run dev

# In a separate terminal, start Remotion Studio (optional)
npm run remotion:dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## 📖 Usage Guide

### Two Ways to Create Videos

#### 1. Chat Interface (AI-Generated Code)
1. **Start a Conversation**: Type your request in the chat input
2. **Generate Code**: Ask Gemini to create code (e.g., "Create a React component for a todo list")
3. **Use Generated Code**: Click "Use in Video" to load code into the video preview
4. **Quick Actions**: Use preset buttons for common code generation tasks

#### 2. Code to Video (Direct Paste)
1. **Switch to "Code to Video" Tab**: Click the tab in the left panel
2. **Paste Your Code**: Directly paste any code you want to turn into a video
3. **Choose Settings**: Select language, theme, and video title
4. **Generate Video**: Click "Generate Video" for instant video creation
5. **Use Examples**: Try the built-in examples (Fibonacci, Quicksort, React Todo)

### Video Controls

- **Play/Pause**: Control video playback
- **Restart**: Reset video to beginning
- **Settings**: Customize theme, language, and title
- **Export**: Generate final video file (requires backend setup)

### Example Prompts

- "Create a Python function to calculate fibonacci numbers"
- "Write a React component with useState hook"
- "Generate a JavaScript API client with error handling"
- "Create a CSS animation for a loading spinner"

## 🎬 Video Customization

### Supported Languages
- JavaScript/TypeScript
- Python
- Java
- C++
- Rust
- Go

### Themes
- **Dark Theme**: Perfect for code presentations
- **Light Theme**: Clean, professional look

### Animation Features
- Fade-in effects for code lines
- Syntax highlighting
- Animated background elements
- Smooth transitions

## 📁 Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Main application page
├── components/            # React components
│   ├── ChatInterface.tsx  # Chat UI component
│   └── VideoControls.tsx  # Video control component
├── hooks/                 # Custom React hooks
│   └── useChat.ts         # Chat management hook
├── lib/                   # Utility libraries
│   ├── gemini.ts          # Gemini API integration
│   └── types.ts           # TypeScript type definitions
└── remotion/              # Remotion video components
    ├── index.ts           # Remotion entry point
    ├── Root.tsx           # Composition root
    └── CodeVideo.tsx      # Main video component
```

## 🔧 Configuration

### Remotion Settings

Edit `remotion.config.ts` to customize:
- Video resolution (default: 1920x1080)
- Frame rate (default: 30 FPS)
- Duration (default: 10 seconds)
- Output quality and codec

### Tailwind Customization

Modify `tailwind.config.js` for:
- Custom color schemes
- Animation timings
- Font configurations
- Responsive breakpoints

## 🚀 Deployment

### Vercel (Recommended)

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel

# Add environment variables in Vercel dashboard
```

### Other Platforms

The application can be deployed to any platform supporting Next.js:
- Netlify
- Railway
- DigitalOcean App Platform
- AWS Amplify

## 🎥 Video Rendering

For production video rendering, you'll need to set up a backend service:

```bash
# Render a video programmatically
npm run remotion:render -- --props='{"code":"console.log(\"Hello World\")","language":"javascript","title":"Hello World"}'
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Remotion](https://remotion.dev) - Amazing video generation framework
- [Google AI](https://ai.google.dev) - Powerful Gemini 2.0 Flash API
- [Next.js](https://nextjs.org) - Excellent React framework
- [Tailwind CSS](https://tailwindcss.com) - Utility-first CSS framework

## 📞 Support

If you encounter any issues or have questions:

1. Check the [Issues](https://github.com/your-repo/issues) page
2. Create a new issue with detailed information
3. Join our community discussions

---

**Happy coding and video creating! 🎬✨**
