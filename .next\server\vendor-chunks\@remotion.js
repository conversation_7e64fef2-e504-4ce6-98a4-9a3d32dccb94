"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@remotion";
exports.ids = ["vendor-chunks/@remotion"];
exports.modules = {

/***/ "(ssr)/./node_modules/@remotion/player/dist/esm/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@remotion/player/dist/esm/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Player: () => (/* binding */ Player),\n/* harmony export */   PlayerInternals: () => (/* binding */ PlayerInternals),\n/* harmony export */   Thumbnail: () => (/* binding */ Thumbnail)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var remotion__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! remotion */ \"(ssr)/./node_modules/remotion/dist/esm/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var remotion_no_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! remotion/no-react */ \"(ssr)/./node_modules/remotion/dist/esm/no-react.mjs\");\n/* __next_internal_client_entry_do_not_use__ Thumbnail,PlayerInternals,Player auto */ // src/icons.tsx\n\nvar ICON_SIZE = 25;\nvar fullscreenIconSize = 16;\nvar PlayIcon = ()=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n        width: ICON_SIZE,\n        height: ICON_SIZE,\n        viewBox: \"0 0 25 25\",\n        fill: \"none\",\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n            d: \"M8 6.375C7.40904 8.17576 7.06921 10.2486 7.01438 12.3871C6.95955 14.5255 7.19163 16.6547 7.6875 18.5625C9.95364 18.2995 12.116 17.6164 14.009 16.5655C15.902 15.5147 17.4755 14.124 18.6088 12.5C17.5158 10.8949 15.9949 9.51103 14.1585 8.45082C12.3222 7.3906 10.2174 6.68116 8 6.375Z\",\n            fill: \"white\",\n            stroke: \"white\",\n            strokeWidth: \"6.25\",\n            strokeLinejoin: \"round\"\n        })\n    });\n};\nvar PauseIcon = ()=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"svg\", {\n        viewBox: \"0 0 100 100\",\n        width: ICON_SIZE,\n        height: ICON_SIZE,\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"rect\", {\n                x: \"25\",\n                y: \"20\",\n                width: \"20\",\n                height: \"60\",\n                fill: \"#fff\",\n                ry: \"5\",\n                rx: \"5\"\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"rect\", {\n                x: \"55\",\n                y: \"20\",\n                width: \"20\",\n                height: \"60\",\n                fill: \"#fff\",\n                ry: \"5\",\n                rx: \"5\"\n            })\n        ]\n    });\n};\nvar FullscreenIcon = ({ isFullscreen })=>{\n    const strokeWidth = 6;\n    const viewSize = 32;\n    const out = isFullscreen ? 0 : strokeWidth / 2;\n    const middleInset = isFullscreen ? strokeWidth * 1.6 : strokeWidth / 2;\n    const inset = isFullscreen ? strokeWidth * 1.6 : strokeWidth * 2;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"svg\", {\n        viewBox: `0 0 ${viewSize} ${viewSize}`,\n        height: fullscreenIconSize,\n        width: fullscreenIconSize,\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                d: `\n\t\t\t\tM ${out} ${inset}\n\t\t\t\tL ${middleInset} ${middleInset}\n\t\t\t\tL ${inset} ${out}\n\t\t\t\t`,\n                stroke: \"#fff\",\n                strokeWidth,\n                fill: \"none\"\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                d: `\n\t\t\t\tM ${viewSize - out} ${inset}\n\t\t\t\tL ${viewSize - middleInset} ${middleInset}\n\t\t\t\tL ${viewSize - inset} ${out}\n\t\t\t\t`,\n                stroke: \"#fff\",\n                strokeWidth,\n                fill: \"none\"\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                d: `\n\t\t\t\tM ${out} ${viewSize - inset}\n\t\t\t\tL ${middleInset} ${viewSize - middleInset}\n\t\t\t\tL ${inset} ${viewSize - out}\n\t\t\t\t`,\n                stroke: \"#fff\",\n                strokeWidth,\n                fill: \"none\"\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                d: `\n\t\t\t\tM ${viewSize - out} ${viewSize - inset}\n\t\t\t\tL ${viewSize - middleInset} ${viewSize - middleInset}\n\t\t\t\tL ${viewSize - inset} ${viewSize - out}\n\t\t\t\t`,\n                stroke: \"#fff\",\n                strokeWidth,\n                fill: \"none\"\n            })\n        ]\n    });\n};\nvar VolumeOffIcon = ()=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n        width: ICON_SIZE,\n        height: ICON_SIZE,\n        viewBox: \"0 0 24 24\",\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n            d: \"M3.63 3.63a.996.996 0 000 1.41L7.29 8.7 7 9H4c-.55 0-1 .45-1 1v4c0 .55.45 1 1 1h3l3.29 3.29c.63.63 1.71.18 1.71-.71v-4.17l4.18 4.18c-.49.37-1.02.68-1.6.91-.36.15-.58.53-.58.92 0 .72.73 1.18 1.39.91.8-.33 1.55-.77 2.22-1.31l1.34 1.34a.996.996 0 101.41-1.41L5.05 3.63c-.39-.39-1.02-.39-1.42 0zM19 12c0 .82-.15 1.61-.41 2.34l1.53 1.53c.56-1.17.88-2.48.88-3.87 0-3.83-2.4-7.11-5.78-8.4-.59-.23-1.22.23-1.22.86v.19c0 .38.25.71.61.85C17.18 6.54 19 9.06 19 12zm-8.71-6.29l-.17.17L12 7.76V6.41c0-.89-1.08-1.33-1.71-.7zM16.5 12A4.5 4.5 0 0014 7.97v1.79l2.48 2.48c.01-.08.02-.16.02-.24z\",\n            fill: \"#fff\"\n        })\n    });\n};\nvar VolumeOnIcon = ()=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n        width: ICON_SIZE,\n        height: ICON_SIZE,\n        viewBox: \"0 0 24 24\",\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n            d: \"M3 10v4c0 .55.45 1 1 1h3l3.29 3.29c.63.63 1.71.18 1.71-.71V6.41c0-.89-1.08-1.34-1.71-.71L7 9H4c-.55 0-1 .45-1 1zm13.5 2A4.5 4.5 0 0014 7.97v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 4.45v.2c0 .38.25.71.6.85C17.18 6.53 19 9.06 19 12s-1.82 5.47-4.4 6.5c-.36.14-.6.47-.6.85v.2c0 .63.63 1.07 1.21.85C18.6 19.11 21 15.84 21 12s-2.4-7.11-5.79-8.4c-.58-.23-1.21.22-1.21.85z\",\n            fill: \"#fff\"\n        })\n    });\n};\n// src/BufferingIndicator.tsx\n\nvar className = \"__remotion_buffering_indicator\";\nvar remotionBufferingAnimation = \"__remotion_buffering_animation\";\nvar playerStyle = {\n    width: ICON_SIZE,\n    height: ICON_SIZE,\n    overflow: \"hidden\",\n    lineHeight: \"normal\",\n    fontSize: \"inherit\"\n};\nvar studioStyle = {\n    width: 14,\n    height: 14,\n    overflow: \"hidden\",\n    lineHeight: \"normal\",\n    fontSize: \"inherit\"\n};\nvar BufferingIndicator = ({ type })=>{\n    const style = type === \"player\" ? playerStyle : studioStyle;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"style\", {\n                type: \"text/css\",\n                children: `\n\t\t\t\t@keyframes ${remotionBufferingAnimation} {\n          0% {\n            rotate: 0deg;\n          }\n          100% {\n            rotate: 360deg;\n          }\n        }\n        \n        .${className} {\n            animation: ${remotionBufferingAnimation} 1s linear infinite;\n        }        \n\t\t\t`\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                style,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n                    viewBox: type === \"player\" ? \"0 0 22 22\" : \"0 0 18 18\",\n                    style,\n                    className,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                        d: type === \"player\" ? \"M 11 4 A 7 7 0 0 1 15.1145 16.66312\" : \"M 9 2 A 7 7 0 0 1 13.1145 14.66312\",\n                        stroke: \"white\",\n                        strokeLinecap: \"round\",\n                        fill: \"none\",\n                        strokeWidth: 3\n                    })\n                })\n            })\n        ]\n    });\n};\n// src/calculate-scale.ts\n\n// src/utils/calculate-player-size.ts\nvar calculatePlayerSize = ({ currentSize, width, height, compositionWidth, compositionHeight })=>{\n    if (width !== undefined && height === undefined) {\n        return {\n            aspectRatio: [\n                compositionWidth,\n                compositionHeight\n            ].join(\"/\")\n        };\n    }\n    if (height !== undefined && width === undefined) {\n        return {\n            aspectRatio: [\n                compositionWidth,\n                compositionHeight\n            ].join(\"/\")\n        };\n    }\n    if (!currentSize) {\n        return {\n            width: compositionWidth,\n            height: compositionHeight\n        };\n    }\n    return {\n        width: compositionWidth,\n        height: compositionHeight\n    };\n};\n// src/calculate-scale.ts\nvar calculateCanvasTransformation = ({ previewSize, compositionWidth, compositionHeight, canvasSize })=>{\n    const scale = remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.calculateScale({\n        canvasSize,\n        compositionHeight,\n        compositionWidth,\n        previewSize\n    });\n    const correction = 0 - (1 - scale) / 2;\n    const xCorrection = correction * compositionWidth;\n    const yCorrection = correction * compositionHeight;\n    const width = compositionWidth * scale;\n    const height = compositionHeight * scale;\n    const centerX = canvasSize.width / 2 - width / 2;\n    const centerY = canvasSize.height / 2 - height / 2;\n    return {\n        centerX,\n        centerY,\n        xCorrection,\n        yCorrection,\n        scale\n    };\n};\nvar calculateOuterStyle = ({ config, style, canvasSize, overflowVisible, layout })=>{\n    if (!config) {\n        return {};\n    }\n    return {\n        position: \"relative\",\n        overflow: overflowVisible ? \"visible\" : \"hidden\",\n        ...calculatePlayerSize({\n            compositionHeight: config.height,\n            compositionWidth: config.width,\n            currentSize: canvasSize,\n            height: style?.height,\n            width: style?.width\n        }),\n        opacity: layout ? 1 : 0,\n        ...style\n    };\n};\nvar calculateContainerStyle = ({ config, layout, scale, overflowVisible })=>{\n    if (!config) {\n        return {};\n    }\n    if (!layout) {\n        return {\n            position: \"absolute\",\n            width: config.width,\n            height: config.height,\n            display: \"flex\",\n            transform: `scale(${scale})`,\n            overflow: overflowVisible ? \"visible\" : \"hidden\"\n        };\n    }\n    return {\n        position: \"absolute\",\n        width: config.width,\n        height: config.height,\n        display: \"flex\",\n        transform: `scale(${scale})`,\n        marginLeft: layout.xCorrection,\n        marginTop: layout.yCorrection,\n        overflow: overflowVisible ? \"visible\" : \"hidden\"\n    };\n};\nvar calculateOuter = ({ layout, scale, config, overflowVisible })=>{\n    if (!config) {\n        return {};\n    }\n    if (!layout) {\n        return {\n            width: config.width * scale,\n            height: config.height * scale,\n            display: \"flex\",\n            flexDirection: \"column\",\n            position: \"absolute\",\n            overflow: overflowVisible ? \"visible\" : \"hidden\"\n        };\n    }\n    const { centerX, centerY } = layout;\n    return {\n        width: config.width * scale,\n        height: config.height * scale,\n        display: \"flex\",\n        flexDirection: \"column\",\n        position: \"absolute\",\n        left: centerX,\n        top: centerY,\n        overflow: overflowVisible ? \"visible\" : \"hidden\"\n    };\n};\n// src/emitter-context.ts\n\nvar PlayerEventEmitterContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createContext(undefined);\nvar ThumbnailEmitterContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_2__.createContext(undefined);\n// src/EmitterProvider.tsx\n\n\n// src/event-emitter.ts\nclass PlayerEmitter {\n    addEventListener(name, callback) {\n        this.listeners[name].push(callback);\n    }\n    removeEventListener(name, callback) {\n        this.listeners[name] = this.listeners[name].filter((l)=>l !== callback);\n    }\n    dispatchEvent(dispatchName, context) {\n        this.listeners[dispatchName].forEach((callback)=>{\n            callback({\n                detail: context\n            });\n        });\n    }\n    constructor(){\n        this.listeners = {\n            ended: [],\n            error: [],\n            pause: [],\n            play: [],\n            ratechange: [],\n            scalechange: [],\n            seeked: [],\n            timeupdate: [],\n            frameupdate: [],\n            fullscreenchange: [],\n            volumechange: [],\n            mutechange: [],\n            waiting: [],\n            resume: []\n        };\n        this.dispatchSeek = (frame)=>{\n            this.dispatchEvent(\"seeked\", {\n                frame\n            });\n        };\n        this.dispatchVolumeChange = (volume)=>{\n            this.dispatchEvent(\"volumechange\", {\n                volume\n            });\n        };\n        this.dispatchPause = ()=>{\n            this.dispatchEvent(\"pause\", undefined);\n        };\n        this.dispatchPlay = ()=>{\n            this.dispatchEvent(\"play\", undefined);\n        };\n        this.dispatchEnded = ()=>{\n            this.dispatchEvent(\"ended\", undefined);\n        };\n        this.dispatchRateChange = (playbackRate)=>{\n            this.dispatchEvent(\"ratechange\", {\n                playbackRate\n            });\n        };\n        this.dispatchScaleChange = (scale)=>{\n            this.dispatchEvent(\"scalechange\", {\n                scale\n            });\n        };\n        this.dispatchError = (error)=>{\n            this.dispatchEvent(\"error\", {\n                error\n            });\n        };\n        this.dispatchTimeUpdate = (event)=>{\n            this.dispatchEvent(\"timeupdate\", event);\n        };\n        this.dispatchFrameUpdate = (event)=>{\n            this.dispatchEvent(\"frameupdate\", event);\n        };\n        this.dispatchFullscreenChange = (event)=>{\n            this.dispatchEvent(\"fullscreenchange\", event);\n        };\n        this.dispatchMuteChange = (event)=>{\n            this.dispatchEvent(\"mutechange\", event);\n        };\n        this.dispatchWaiting = (event)=>{\n            this.dispatchEvent(\"waiting\", event);\n        };\n        this.dispatchResume = (event)=>{\n            this.dispatchEvent(\"resume\", event);\n        };\n    }\n}\nclass ThumbnailEmitter {\n    addEventListener(name, callback) {\n        this.listeners[name].push(callback);\n    }\n    removeEventListener(name, callback) {\n        this.listeners[name] = this.listeners[name].filter((l)=>l !== callback);\n    }\n    dispatchEvent(dispatchName, context) {\n        this.listeners[dispatchName].forEach((callback)=>{\n            callback({\n                detail: context\n            });\n        });\n    }\n    constructor(){\n        this.listeners = {\n            error: [],\n            waiting: [],\n            resume: []\n        };\n        this.dispatchError = (error)=>{\n            this.dispatchEvent(\"error\", {\n                error\n            });\n        };\n        this.dispatchWaiting = (event)=>{\n            this.dispatchEvent(\"waiting\", event);\n        };\n        this.dispatchResume = (event)=>{\n            this.dispatchEvent(\"resume\", event);\n        };\n    }\n}\n// src/use-buffer-state-emitter.ts\n\n\nvar useBufferStateEmitter = (emitter)=>{\n    const bufferManager = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.BufferingContextReact);\n    if (!bufferManager) {\n        throw new Error(\"BufferingContextReact not found\");\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const clear1 = bufferManager.listenForBuffering(()=>{\n            bufferManager.buffering.current = true;\n            emitter.dispatchWaiting({});\n        });\n        const clear2 = bufferManager.listenForResume(()=>{\n            bufferManager.buffering.current = false;\n            emitter.dispatchResume({});\n        });\n        return ()=>{\n            clear1.remove();\n            clear2.remove();\n        };\n    }, [\n        bufferManager,\n        emitter\n    ]);\n};\n// src/EmitterProvider.tsx\n\nvar PlayerEmitterProvider = ({ children, currentPlaybackRate })=>{\n    const [emitter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(()=>new PlayerEmitter);\n    const bufferManager = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.BufferingContextReact);\n    if (!bufferManager) {\n        throw new Error(\"BufferingContextReact not found\");\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (currentPlaybackRate) {\n            emitter.dispatchRateChange(currentPlaybackRate);\n        }\n    }, [\n        emitter,\n        currentPlaybackRate\n    ]);\n    useBufferStateEmitter(emitter);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PlayerEventEmitterContext.Provider, {\n        value: emitter,\n        children\n    });\n};\n// src/use-frame-imperative.ts\n\n\nvar useFrameImperative = ()=>{\n    const frame = remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.Timeline.useTimelinePosition();\n    const frameRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(frame);\n    frameRef.current = frame;\n    const getCurrentFrame = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        return frameRef.current;\n    }, []);\n    return getCurrentFrame;\n};\n// src/use-hover-state.ts\n\nvar useHoverState = (ref, hideControlsWhenPointerDoesntMove)=>{\n    const [hovered, setHovered] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const { current } = ref;\n        if (!current) {\n            return;\n        }\n        let hoverTimeout;\n        const addHoverTimeout = ()=>{\n            if (hideControlsWhenPointerDoesntMove) {\n                clearTimeout(hoverTimeout);\n                hoverTimeout = setTimeout(()=>{\n                    setHovered(false);\n                }, hideControlsWhenPointerDoesntMove === true ? 3000 : hideControlsWhenPointerDoesntMove);\n            }\n        };\n        const onHover = ()=>{\n            setHovered(true);\n            addHoverTimeout();\n        };\n        const onLeave = ()=>{\n            setHovered(false);\n            clearTimeout(hoverTimeout);\n        };\n        const onMove = ()=>{\n            setHovered(true);\n            addHoverTimeout();\n        };\n        current.addEventListener(\"mouseenter\", onHover);\n        current.addEventListener(\"mouseleave\", onLeave);\n        current.addEventListener(\"mousemove\", onMove);\n        return ()=>{\n            current.removeEventListener(\"mouseenter\", onHover);\n            current.removeEventListener(\"mouseleave\", onLeave);\n            current.removeEventListener(\"mousemove\", onMove);\n            clearTimeout(hoverTimeout);\n        };\n    }, [\n        hideControlsWhenPointerDoesntMove,\n        ref\n    ]);\n    return hovered;\n};\n// src/use-playback.ts\n\n\n// src/browser-mediasession.ts\n\n// src/use-player.ts\n\n\nvar usePlayer = ()=>{\n    const [playing, setPlaying, imperativePlaying] = remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.Timeline.usePlayingState();\n    const [hasPlayed, setHasPlayed] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const frame = remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.Timeline.useTimelinePosition();\n    const playStart = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(frame);\n    const setFrame = remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.Timeline.useTimelineSetFrame();\n    const setTimelinePosition = remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.Timeline.useTimelineSetFrame();\n    const audioContext = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.SharedAudioContext);\n    const { audioAndVideoTags } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.Timeline.TimelineContext);\n    const frameRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(frame);\n    frameRef.current = frame;\n    const video = remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.useVideo();\n    const config = remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.useUnsafeVideoConfig();\n    const emitter = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(PlayerEventEmitterContext);\n    const lastFrame = (config?.durationInFrames ?? 1) - 1;\n    const isLastFrame = frame === lastFrame;\n    const isFirstFrame = frame === 0;\n    if (!emitter) {\n        throw new TypeError(\"Expected Player event emitter context\");\n    }\n    const bufferingContext = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.BufferingContextReact);\n    if (!bufferingContext) {\n        throw new Error(\"Missing the buffering context. Most likely you have a Remotion version mismatch.\");\n    }\n    const { buffering } = bufferingContext;\n    const seek = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((newFrame)=>{\n        if (video?.id) {\n            setTimelinePosition((c)=>({\n                    ...c,\n                    [video.id]: newFrame\n                }));\n        }\n        frameRef.current = newFrame;\n        emitter.dispatchSeek(newFrame);\n    }, [\n        emitter,\n        setTimelinePosition,\n        video?.id\n    ]);\n    const play = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        if (imperativePlaying.current) {\n            return;\n        }\n        setHasPlayed(true);\n        if (isLastFrame) {\n            seek(0);\n        }\n        audioContext?.audioContext?.resume();\n        if (audioContext && audioContext.numberOfAudioTags > 0 && e) {\n            audioContext.playAllAudios();\n        }\n        audioAndVideoTags.current.forEach((a)=>a.play(\"player play() was called and playing audio from a click\"));\n        imperativePlaying.current = true;\n        setPlaying(true);\n        playStart.current = frameRef.current;\n        emitter.dispatchPlay();\n    }, [\n        imperativePlaying,\n        isLastFrame,\n        audioContext,\n        setPlaying,\n        emitter,\n        seek,\n        audioAndVideoTags\n    ]);\n    const pause = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        if (imperativePlaying.current) {\n            imperativePlaying.current = false;\n            setPlaying(false);\n            emitter.dispatchPause();\n            audioContext?.audioContext?.suspend();\n        }\n    }, [\n        emitter,\n        imperativePlaying,\n        setPlaying,\n        audioContext\n    ]);\n    const pauseAndReturnToPlayStart = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        if (imperativePlaying.current) {\n            imperativePlaying.current = false;\n            frameRef.current = playStart.current;\n            if (config) {\n                setTimelinePosition((c)=>({\n                        ...c,\n                        [config.id]: playStart.current\n                    }));\n                setPlaying(false);\n                emitter.dispatchPause();\n            }\n        }\n    }, [\n        config,\n        emitter,\n        imperativePlaying,\n        setPlaying,\n        setTimelinePosition\n    ]);\n    const videoId = video?.id;\n    const frameBack = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((frames)=>{\n        if (!videoId) {\n            return null;\n        }\n        if (imperativePlaying.current) {\n            return;\n        }\n        setFrame((c)=>{\n            const prevFrame = c[videoId] ?? window.remotion_initialFrame ?? 0;\n            const newFrame = Math.max(0, prevFrame - frames);\n            if (prevFrame === newFrame) {\n                return c;\n            }\n            return {\n                ...c,\n                [videoId]: newFrame\n            };\n        });\n    }, [\n        imperativePlaying,\n        setFrame,\n        videoId\n    ]);\n    const frameForward = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((frames)=>{\n        if (!videoId) {\n            return null;\n        }\n        if (imperativePlaying.current) {\n            return;\n        }\n        setFrame((c)=>{\n            const prevFrame = c[videoId] ?? window.remotion_initialFrame ?? 0;\n            const newFrame = Math.min(lastFrame, prevFrame + frames);\n            if (prevFrame === newFrame) {\n                return c;\n            }\n            return {\n                ...c,\n                [videoId]: newFrame\n            };\n        });\n    }, [\n        videoId,\n        imperativePlaying,\n        lastFrame,\n        setFrame\n    ]);\n    const toggle = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        if (imperativePlaying.current) {\n            pause();\n        } else {\n            play(e);\n        }\n    }, [\n        imperativePlaying,\n        pause,\n        play\n    ]);\n    const returnValue = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            frameBack,\n            frameForward,\n            isLastFrame,\n            emitter,\n            playing,\n            play,\n            pause,\n            seek,\n            isFirstFrame,\n            getCurrentFrame: ()=>frameRef.current,\n            isPlaying: ()=>imperativePlaying.current,\n            isBuffering: ()=>buffering.current,\n            pauseAndReturnToPlayStart,\n            hasPlayed,\n            toggle\n        };\n    }, [\n        buffering,\n        emitter,\n        frameBack,\n        frameForward,\n        hasPlayed,\n        imperativePlaying,\n        isFirstFrame,\n        isLastFrame,\n        pause,\n        pauseAndReturnToPlayStart,\n        play,\n        playing,\n        seek,\n        toggle\n    ]);\n    return returnValue;\n};\n// src/browser-mediasession.ts\nvar useBrowserMediaSession = ({ browserMediaControlsBehavior, videoConfig, playbackRate })=>{\n    const { playing, pause, play, emitter, getCurrentFrame, seek } = usePlayer();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!navigator.mediaSession) {\n            return;\n        }\n        if (browserMediaControlsBehavior.mode === \"do-nothing\") {\n            return;\n        }\n        if (playing) {\n            navigator.mediaSession.playbackState = \"playing\";\n        } else {\n            navigator.mediaSession.playbackState = \"paused\";\n        }\n    }, [\n        browserMediaControlsBehavior.mode,\n        playing\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!navigator.mediaSession) {\n            return;\n        }\n        if (browserMediaControlsBehavior.mode === \"do-nothing\") {\n            return;\n        }\n        const onTimeUpdate = ()=>{\n            if (!videoConfig) {\n                return;\n            }\n            if (navigator.mediaSession) {\n                navigator.mediaSession.setPositionState({\n                    duration: videoConfig.durationInFrames / videoConfig.fps,\n                    playbackRate,\n                    position: getCurrentFrame() / videoConfig.fps\n                });\n            }\n        };\n        emitter.addEventListener(\"timeupdate\", onTimeUpdate);\n        return ()=>{\n            emitter.removeEventListener(\"timeupdate\", onTimeUpdate);\n        };\n    }, [\n        browserMediaControlsBehavior.mode,\n        emitter,\n        getCurrentFrame,\n        playbackRate,\n        videoConfig\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!navigator.mediaSession) {\n            return;\n        }\n        if (browserMediaControlsBehavior.mode === \"do-nothing\") {\n            return;\n        }\n        navigator.mediaSession.setActionHandler(\"play\", ()=>{\n            if (browserMediaControlsBehavior.mode === \"register-media-session\") {\n                play();\n            }\n        });\n        navigator.mediaSession.setActionHandler(\"pause\", ()=>{\n            if (browserMediaControlsBehavior.mode === \"register-media-session\") {\n                pause();\n            }\n        });\n        navigator.mediaSession.setActionHandler(\"seekto\", (event)=>{\n            if (browserMediaControlsBehavior.mode === \"register-media-session\" && event.seekTime !== undefined && videoConfig) {\n                seek(Math.round(event.seekTime * videoConfig.fps));\n            }\n        });\n        navigator.mediaSession.setActionHandler(\"seekbackward\", ()=>{\n            if (browserMediaControlsBehavior.mode === \"register-media-session\" && videoConfig) {\n                seek(Math.max(0, Math.round((getCurrentFrame() - 10) * videoConfig.fps)));\n            }\n        });\n        navigator.mediaSession.setActionHandler(\"seekforward\", ()=>{\n            if (browserMediaControlsBehavior.mode === \"register-media-session\" && videoConfig) {\n                seek(Math.max(videoConfig.durationInFrames - 1, Math.round((getCurrentFrame() + 10) * videoConfig.fps)));\n            }\n        });\n        navigator.mediaSession.setActionHandler(\"previoustrack\", ()=>{\n            if (browserMediaControlsBehavior.mode === \"register-media-session\") {\n                seek(0);\n            }\n        });\n        return ()=>{\n            navigator.mediaSession.metadata = null;\n            navigator.mediaSession.setActionHandler(\"play\", null);\n            navigator.mediaSession.setActionHandler(\"pause\", null);\n            navigator.mediaSession.setActionHandler(\"seekto\", null);\n            navigator.mediaSession.setActionHandler(\"seekbackward\", null);\n            navigator.mediaSession.setActionHandler(\"seekforward\", null);\n            navigator.mediaSession.setActionHandler(\"previoustrack\", null);\n        };\n    }, [\n        browserMediaControlsBehavior.mode,\n        getCurrentFrame,\n        pause,\n        play,\n        seek,\n        videoConfig\n    ]);\n};\n// src/calculate-next-frame.ts\nvar calculateNextFrame = ({ time, currentFrame: startFrame, playbackSpeed, fps, actualLastFrame, actualFirstFrame, framesAdvanced, shouldLoop })=>{\n    const op = playbackSpeed < 0 ? Math.ceil : Math.floor;\n    const framesToAdvance = op(time * playbackSpeed / (1000 / fps)) - framesAdvanced;\n    const nextFrame = framesToAdvance + startFrame;\n    const isCurrentFrameOutside = startFrame > actualLastFrame || startFrame < actualFirstFrame;\n    const isNextFrameOutside = nextFrame > actualLastFrame || nextFrame < actualFirstFrame;\n    const hasEnded = !shouldLoop && isNextFrameOutside && !isCurrentFrameOutside;\n    if (playbackSpeed > 0) {\n        if (isNextFrameOutside) {\n            return {\n                nextFrame: actualFirstFrame,\n                framesToAdvance,\n                hasEnded\n            };\n        }\n        return {\n            nextFrame,\n            framesToAdvance,\n            hasEnded\n        };\n    }\n    if (isNextFrameOutside) {\n        return {\n            nextFrame: actualLastFrame,\n            framesToAdvance,\n            hasEnded\n        };\n    }\n    return {\n        nextFrame,\n        framesToAdvance,\n        hasEnded\n    };\n};\n// src/is-backgrounded.ts\n\nvar getIsBackgrounded = ()=>{\n    if (typeof document === \"undefined\") {\n        return false;\n    }\n    return document.visibilityState === \"hidden\";\n};\nvar useIsBackgrounded = ()=>{\n    const isBackgrounded = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(getIsBackgrounded());\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const onVisibilityChange = ()=>{\n            isBackgrounded.current = getIsBackgrounded();\n        };\n        document.addEventListener(\"visibilitychange\", onVisibilityChange);\n        return ()=>{\n            document.removeEventListener(\"visibilitychange\", onVisibilityChange);\n        };\n    }, []);\n    return isBackgrounded;\n};\n// src/use-playback.ts\nvar usePlayback = ({ loop, playbackRate, moveToBeginningWhenEnded, inFrame, outFrame, browserMediaControlsBehavior, getCurrentFrame })=>{\n    const config = remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.useUnsafeVideoConfig();\n    const frame = remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.Timeline.useTimelinePosition();\n    const { playing, pause, emitter } = usePlayer();\n    const setFrame = remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.Timeline.useTimelineSetFrame();\n    const isBackgroundedRef = useIsBackgrounded();\n    const lastTimeUpdateEvent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.BufferingContextReact);\n    if (!context) {\n        throw new Error(\"Missing the buffering context. Most likely you have a Remotion version mismatch.\");\n    }\n    useBrowserMediaSession({\n        browserMediaControlsBehavior,\n        playbackRate,\n        videoConfig: config\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!config) {\n            return;\n        }\n        if (!playing) {\n            return;\n        }\n        let hasBeenStopped = false;\n        let reqAnimFrameCall = null;\n        let startedTime = performance.now();\n        let framesAdvanced = 0;\n        const cancelQueuedFrame = ()=>{\n            if (reqAnimFrameCall !== null) {\n                if (reqAnimFrameCall.type === \"raf\") {\n                    cancelAnimationFrame(reqAnimFrameCall.id);\n                } else {\n                    clearTimeout(reqAnimFrameCall.id);\n                }\n            }\n        };\n        const stop = ()=>{\n            hasBeenStopped = true;\n            cancelQueuedFrame();\n        };\n        const callback = ()=>{\n            if (hasBeenStopped) {\n                return;\n            }\n            const time = performance.now() - startedTime;\n            const actualLastFrame = outFrame ?? config.durationInFrames - 1;\n            const actualFirstFrame = inFrame ?? 0;\n            const currentFrame = getCurrentFrame();\n            const { nextFrame, framesToAdvance, hasEnded } = calculateNextFrame({\n                time,\n                currentFrame,\n                playbackSpeed: playbackRate,\n                fps: config.fps,\n                actualFirstFrame,\n                actualLastFrame,\n                framesAdvanced,\n                shouldLoop: loop\n            });\n            framesAdvanced += framesToAdvance;\n            if (nextFrame !== getCurrentFrame() && (!hasEnded || moveToBeginningWhenEnded)) {\n                setFrame((c)=>({\n                        ...c,\n                        [config.id]: nextFrame\n                    }));\n            }\n            if (hasEnded) {\n                stop();\n                pause();\n                emitter.dispatchEnded();\n                return;\n            }\n            queueNextFrame();\n        };\n        const queueNextFrame = ()=>{\n            if (context.buffering.current) {\n                const stopListening = context.listenForResume(()=>{\n                    stopListening.remove();\n                    startedTime = performance.now();\n                    framesAdvanced = 0;\n                    queueNextFrame();\n                });\n                return;\n            }\n            if (isBackgroundedRef.current) {\n                reqAnimFrameCall = {\n                    type: \"timeout\",\n                    id: setTimeout(callback, 1000 / config.fps)\n                };\n                return;\n            }\n            reqAnimFrameCall = {\n                type: \"raf\",\n                id: requestAnimationFrame(callback)\n            };\n        };\n        queueNextFrame();\n        const onVisibilityChange = ()=>{\n            if (document.visibilityState === \"visible\") {\n                return;\n            }\n            cancelQueuedFrame();\n            callback();\n        };\n        window.addEventListener(\"visibilitychange\", onVisibilityChange);\n        return ()=>{\n            window.removeEventListener(\"visibilitychange\", onVisibilityChange);\n            stop();\n        };\n    }, [\n        config,\n        loop,\n        pause,\n        playing,\n        setFrame,\n        emitter,\n        playbackRate,\n        inFrame,\n        outFrame,\n        moveToBeginningWhenEnded,\n        isBackgroundedRef,\n        getCurrentFrame,\n        context\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const interval = setInterval(()=>{\n            if (lastTimeUpdateEvent.current === getCurrentFrame()) {\n                return;\n            }\n            emitter.dispatchTimeUpdate({\n                frame: getCurrentFrame()\n            });\n            lastTimeUpdateEvent.current = getCurrentFrame();\n        }, 250);\n        return ()=>clearInterval(interval);\n    }, [\n        emitter,\n        getCurrentFrame\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        emitter.dispatchFrameUpdate({\n            frame\n        });\n    }, [\n        emitter,\n        frame\n    ]);\n};\n// src/utils/use-element-size.ts\n\nvar elementSizeHooks = [];\nvar updateAllElementsSizes = ()=>{\n    for (const listener of elementSizeHooks){\n        listener();\n    }\n};\nvar useElementSize = (ref, options)=>{\n    const [size, setSize] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(()=>{\n        if (!ref.current) {\n            return null;\n        }\n        const rect = ref.current.getClientRects();\n        if (!rect[0]) {\n            return null;\n        }\n        return {\n            width: rect[0].width,\n            height: rect[0].height,\n            left: rect[0].x,\n            top: rect[0].y,\n            windowSize: {\n                height: window.innerHeight,\n                width: window.innerWidth\n            }\n        };\n    });\n    const observer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (typeof ResizeObserver === \"undefined\") {\n            return null;\n        }\n        return new ResizeObserver((entries)=>{\n            const { contentRect, target } = entries[0];\n            const newSize = target.getClientRects();\n            if (!newSize?.[0]) {\n                setSize(null);\n                return;\n            }\n            const probableCssParentScale = contentRect.width === 0 ? 1 : newSize[0].width / contentRect.width;\n            const width = options.shouldApplyCssTransforms || probableCssParentScale === 0 ? newSize[0].width : newSize[0].width * (1 / probableCssParentScale);\n            const height = options.shouldApplyCssTransforms || probableCssParentScale === 0 ? newSize[0].height : newSize[0].height * (1 / probableCssParentScale);\n            setSize((prevState)=>{\n                const isSame = prevState && prevState.width === width && prevState.height === height && prevState.left === newSize[0].x && prevState.top === newSize[0].y && prevState.windowSize.height === window.innerHeight && prevState.windowSize.width === window.innerWidth;\n                if (isSame) {\n                    return prevState;\n                }\n                return {\n                    width,\n                    height,\n                    left: newSize[0].x,\n                    top: newSize[0].y,\n                    windowSize: {\n                        height: window.innerHeight,\n                        width: window.innerWidth\n                    }\n                };\n            });\n        });\n    }, [\n        options.shouldApplyCssTransforms\n    ]);\n    const updateSize = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        if (!ref.current) {\n            return;\n        }\n        const rect = ref.current.getClientRects();\n        if (!rect[0]) {\n            setSize(null);\n            return;\n        }\n        setSize((prevState)=>{\n            const isSame = prevState && prevState.width === rect[0].width && prevState.height === rect[0].height && prevState.left === rect[0].x && prevState.top === rect[0].y && prevState.windowSize.height === window.innerHeight && prevState.windowSize.width === window.innerWidth;\n            if (isSame) {\n                return prevState;\n            }\n            return {\n                width: rect[0].width,\n                height: rect[0].height,\n                left: rect[0].x,\n                top: rect[0].y,\n                windowSize: {\n                    height: window.innerHeight,\n                    width: window.innerWidth\n                }\n            };\n        });\n    }, [\n        ref\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!observer) {\n            return;\n        }\n        const { current } = ref;\n        if (current) {\n            observer.observe(current);\n        }\n        return ()=>{\n            if (current) {\n                observer.unobserve(current);\n            }\n        };\n    }, [\n        observer,\n        ref,\n        updateSize\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!options.triggerOnWindowResize) {\n            return;\n        }\n        window.addEventListener(\"resize\", updateSize);\n        return ()=>{\n            window.removeEventListener(\"resize\", updateSize);\n        };\n    }, [\n        options.triggerOnWindowResize,\n        updateSize\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        elementSizeHooks.push(updateSize);\n        return ()=>{\n            elementSizeHooks = elementSizeHooks.filter((e)=>e !== updateSize);\n        };\n    }, [\n        updateSize\n    ]);\n    return (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (!size) {\n            return null;\n        }\n        return {\n            ...size,\n            refresh: updateSize\n        };\n    }, [\n        size,\n        updateSize\n    ]);\n};\n// src/Player.tsx\n\n\n// src/PlayerUI.tsx\n\n\n// src/PlayerControls.tsx\n\n// src/DefaultPlayPauseButton.tsx\n\nvar DefaultPlayPauseButton = ({ playing, buffering })=>{\n    if (playing && buffering) {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(BufferingIndicator, {\n            type: \"player\"\n        });\n    }\n    if (playing) {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PauseIcon, {});\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PlayIcon, {});\n};\n// src/MediaVolumeSlider.tsx\n\n\n// src/render-volume-slider.tsx\n\n\n\nvar KNOB_SIZE = 12;\nvar BAR_HEIGHT = 5;\nvar DefaultVolumeSlider = ({ volume, isVertical, onBlur, inputRef, setVolume })=>{\n    const sliderContainer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        const paddingLeft = 5;\n        const common = {\n            paddingLeft,\n            height: ICON_SIZE,\n            width: VOLUME_SLIDER_WIDTH,\n            display: \"inline-flex\",\n            alignItems: \"center\"\n        };\n        if (isVertical) {\n            return {\n                ...common,\n                position: \"absolute\",\n                transform: `rotate(-90deg) translateX(${VOLUME_SLIDER_WIDTH / 2 + ICON_SIZE / 2}px)`\n            };\n        }\n        return {\n            ...common\n        };\n    }, [\n        isVertical\n    ]);\n    const randomId = typeof react__WEBPACK_IMPORTED_MODULE_2__.useId === \"undefined\" ? \"volume-slider\" : react__WEBPACK_IMPORTED_MODULE_2__.useId();\n    const [randomClass] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(()=>`__remotion-volume-slider-${(0,remotion__WEBPACK_IMPORTED_MODULE_1__.random)(randomId)}`.replace(\".\", \"\"));\n    const onVolumeChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        setVolume(parseFloat(e.target.value));\n    }, [\n        setVolume\n    ]);\n    const inputStyle = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        const commonStyle = {\n            WebkitAppearance: \"none\",\n            backgroundColor: \"rgba(255, 255, 255, 0.5)\",\n            borderRadius: BAR_HEIGHT / 2,\n            cursor: \"pointer\",\n            height: BAR_HEIGHT,\n            width: VOLUME_SLIDER_WIDTH,\n            backgroundImage: `linear-gradient(\n\t\t\t\tto right,\n\t\t\t\twhite ${volume * 100}%, rgba(255, 255, 255, 0) ${volume * 100}%\n\t\t\t)`\n        };\n        if (isVertical) {\n            return {\n                ...commonStyle,\n                bottom: ICON_SIZE + VOLUME_SLIDER_WIDTH / 2\n            };\n        }\n        return commonStyle;\n    }, [\n        isVertical,\n        volume\n    ]);\n    const sliderStyle = `\n\t.${randomClass}::-webkit-slider-thumb {\n\t\t-webkit-appearance: none;\n\t\tbackground-color: white;\n\t\tborder-radius: ${KNOB_SIZE / 2}px;\n\t\tbox-shadow: 0 0 2px black;\n\t\theight: ${KNOB_SIZE}px;\n\t\twidth: ${KNOB_SIZE}px;\n\t}\n\n\t.${randomClass}::-moz-range-thumb {\n\t\t-webkit-appearance: none;\n\t\tbackground-color: white;\n\t\tborder-radius: ${KNOB_SIZE / 2}px;\n\t\tbox-shadow: 0 0 2px black;\n\t\theight: ${KNOB_SIZE}px;\n\t\twidth: ${KNOB_SIZE}px;\n\t}\n`;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        style: sliderContainer,\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: sliderStyle\n                }\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"input\", {\n                ref: inputRef,\n                \"aria-label\": \"Change volume\",\n                className: randomClass,\n                max: 1,\n                min: 0,\n                onBlur,\n                onChange: onVolumeChange,\n                step: 0.01,\n                type: \"range\",\n                value: volume,\n                style: inputStyle\n            })\n        ]\n    });\n};\nvar renderDefaultVolumeSlider = (props)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DefaultVolumeSlider, {\n        ...props\n    });\n};\n// src/MediaVolumeSlider.tsx\n\nvar VOLUME_SLIDER_WIDTH = 100;\nvar MediaVolumeSlider = ({ displayVerticalVolumeSlider, renderMuteButton, renderVolumeSlider })=>{\n    const [mediaMuted, setMediaMuted] = remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.useMediaMutedState();\n    const [mediaVolume, setMediaVolume] = remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.useMediaVolumeState();\n    const [focused, setFocused] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const parentDivRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const hover = useHoverState(parentDivRef, false);\n    const onBlur = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        setTimeout(()=>{\n            if (inputRef.current && document.activeElement !== inputRef.current) {\n                setFocused(false);\n            }\n        }, 10);\n    }, []);\n    const isVolume0 = mediaVolume === 0;\n    const onClick = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        if (isVolume0) {\n            setMediaVolume(1);\n            setMediaMuted(false);\n            return;\n        }\n        setMediaMuted((mute)=>!mute);\n    }, [\n        isVolume0,\n        setMediaMuted,\n        setMediaVolume\n    ]);\n    const parentDivStyle = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            display: \"inline-flex\",\n            background: \"none\",\n            border: \"none\",\n            justifyContent: \"center\",\n            alignItems: \"center\",\n            touchAction: \"none\",\n            ...displayVerticalVolumeSlider && {\n                position: \"relative\"\n            }\n        };\n    }, [\n        displayVerticalVolumeSlider\n    ]);\n    const volumeContainer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            display: \"inline\",\n            width: ICON_SIZE,\n            height: ICON_SIZE,\n            cursor: \"pointer\",\n            appearance: \"none\",\n            background: \"none\",\n            border: \"none\",\n            padding: 0\n        };\n    }, []);\n    const renderDefaultMuteButton = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(({ muted, volume })=>{\n        const isMutedOrZero = muted || volume === 0;\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"button\", {\n            \"aria-label\": isMutedOrZero ? \"Unmute sound\" : \"Mute sound\",\n            title: isMutedOrZero ? \"Unmute sound\" : \"Mute sound\",\n            onClick,\n            onBlur,\n            onFocus: ()=>setFocused(true),\n            style: volumeContainer,\n            type: \"button\",\n            children: isMutedOrZero ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(VolumeOffIcon, {}) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(VolumeOnIcon, {})\n        });\n    }, [\n        onBlur,\n        onClick,\n        volumeContainer\n    ]);\n    const muteButton = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return renderMuteButton ? renderMuteButton({\n            muted: mediaMuted,\n            volume: mediaVolume\n        }) : renderDefaultMuteButton({\n            muted: mediaMuted,\n            volume: mediaVolume\n        });\n    }, [\n        mediaMuted,\n        mediaVolume,\n        renderDefaultMuteButton,\n        renderMuteButton\n    ]);\n    const volumeSlider = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return (focused || hover) && !mediaMuted && !remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.isIosSafari() ? (renderVolumeSlider ?? renderDefaultVolumeSlider)({\n            isVertical: displayVerticalVolumeSlider,\n            volume: mediaVolume,\n            onBlur: ()=>setFocused(false),\n            inputRef,\n            setVolume: setMediaVolume\n        }) : null;\n    }, [\n        displayVerticalVolumeSlider,\n        focused,\n        hover,\n        mediaMuted,\n        mediaVolume,\n        renderVolumeSlider,\n        setMediaVolume\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        ref: parentDivRef,\n        style: parentDivStyle,\n        children: [\n            muteButton,\n            volumeSlider\n        ]\n    });\n};\n// src/PlaybackrateControl.tsx\n\n\n// src/utils/use-component-visible.ts\n\nfunction useComponentVisible(initialIsVisible) {\n    const [isComponentVisible, setIsComponentVisible] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(initialIsVisible);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (ref.current && !ref.current.contains(event.target)) {\n                setIsComponentVisible(false);\n            }\n        };\n        document.addEventListener(\"pointerup\", handleClickOutside, true);\n        return ()=>{\n            document.removeEventListener(\"pointerup\", handleClickOutside, true);\n        };\n    }, []);\n    return {\n        ref,\n        isComponentVisible,\n        setIsComponentVisible\n    };\n}\n// src/PlaybackrateControl.tsx\n\nvar BOTTOM = 35;\nvar THRESHOLD = 70;\nvar rateDiv = {\n    height: 30,\n    paddingRight: 15,\n    paddingLeft: 12,\n    display: \"flex\",\n    flexDirection: \"row\",\n    alignItems: \"center\"\n};\nvar checkmarkContainer = {\n    width: 22,\n    display: \"flex\",\n    alignItems: \"center\"\n};\nvar checkmarkStyle = {\n    width: 14,\n    height: 14,\n    color: \"black\"\n};\nvar Checkmark = ()=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n        viewBox: \"0 0 512 512\",\n        style: checkmarkStyle,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n            fill: \"currentColor\",\n            d: \"M435.848 83.466L172.804 346.51l-96.652-96.652c-4.686-4.686-12.284-4.686-16.971 0l-28.284 28.284c-4.686 4.686-4.686 12.284 0 16.971l133.421 133.421c4.686 4.686 12.284 4.686 16.971 0l299.813-299.813c4.686-4.686 4.686-12.284 0-16.971l-28.284-28.284c-4.686-4.686-12.284-4.686-16.97 0z\"\n        })\n    });\nvar formatPlaybackRate = (rate)=>{\n    const str = rate.toString();\n    return str.includes(\".\") ? str : str + \".0\";\n};\nvar PlaybackrateOption = ({ rate, onSelect, selectedRate, keyboardSelectedRate })=>{\n    const onClick = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        e.stopPropagation();\n        e.preventDefault();\n        onSelect(rate);\n    }, [\n        onSelect,\n        rate\n    ]);\n    const [hovered, setHovered] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const onMouseEnter = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        setHovered(true);\n    }, []);\n    const onMouseLeave = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        setHovered(false);\n    }, []);\n    const isFocused = keyboardSelectedRate === rate;\n    const actualStyle = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            ...rateDiv,\n            backgroundColor: hovered || isFocused ? \"#eee\" : \"transparent\"\n        };\n    }, [\n        hovered,\n        isFocused\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        onMouseEnter,\n        onMouseLeave,\n        tabIndex: 0,\n        style: actualStyle,\n        onClick,\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                style: checkmarkContainer,\n                children: rate === selectedRate ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Checkmark, {}) : null\n            }),\n            formatPlaybackRate(rate),\n            \"x\"\n        ]\n    }, rate);\n};\nvar PlaybackPopup = ({ setIsComponentVisible, playbackRates, canvasSize })=>{\n    const { setPlaybackRate, playbackRate } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.Timeline.TimelineContext);\n    const [keyboardSelectedRate, setKeyboardSelectedRate] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(playbackRate);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const listener = (e)=>{\n            e.preventDefault();\n            if (e.key === \"ArrowUp\") {\n                const currentIndex = playbackRates.findIndex((rate)=>rate === keyboardSelectedRate);\n                if (currentIndex === 0) {\n                    return;\n                }\n                if (currentIndex === -1) {\n                    setKeyboardSelectedRate(playbackRates[0]);\n                } else {\n                    setKeyboardSelectedRate(playbackRates[currentIndex - 1]);\n                }\n            } else if (e.key === \"ArrowDown\") {\n                const currentIndex = playbackRates.findIndex((rate)=>rate === keyboardSelectedRate);\n                if (currentIndex === playbackRates.length - 1) {\n                    return;\n                }\n                if (currentIndex === -1) {\n                    setKeyboardSelectedRate(playbackRates[playbackRates.length - 1]);\n                } else {\n                    setKeyboardSelectedRate(playbackRates[currentIndex + 1]);\n                }\n            } else if (e.key === \"Enter\") {\n                setPlaybackRate(keyboardSelectedRate);\n                setIsComponentVisible(false);\n            }\n        };\n        window.addEventListener(\"keydown\", listener);\n        return ()=>{\n            window.removeEventListener(\"keydown\", listener);\n        };\n    }, [\n        playbackRates,\n        keyboardSelectedRate,\n        setPlaybackRate,\n        setIsComponentVisible\n    ]);\n    const onSelect = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((rate)=>{\n        setPlaybackRate(rate);\n        setIsComponentVisible(false);\n    }, [\n        setIsComponentVisible,\n        setPlaybackRate\n    ]);\n    const playbackPopup = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            position: \"absolute\",\n            right: 0,\n            width: 125,\n            maxHeight: canvasSize.height - THRESHOLD - BOTTOM,\n            bottom: 35,\n            background: \"#fff\",\n            borderRadius: 4,\n            overflow: \"auto\",\n            color: \"black\",\n            textAlign: \"left\"\n        };\n    }, [\n        canvasSize.height\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        style: playbackPopup,\n        children: playbackRates.map((rate)=>{\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PlaybackrateOption, {\n                selectedRate: playbackRate,\n                onSelect,\n                rate,\n                keyboardSelectedRate\n            }, rate);\n        })\n    });\n};\nvar label = {\n    fontSize: 13,\n    fontWeight: \"bold\",\n    color: \"white\",\n    border: \"2px solid white\",\n    borderRadius: 20,\n    paddingLeft: 8,\n    paddingRight: 8,\n    paddingTop: 2,\n    paddingBottom: 2\n};\nvar playerButtonStyle = {\n    appearance: \"none\",\n    backgroundColor: \"transparent\",\n    border: \"none\",\n    cursor: \"pointer\",\n    paddingLeft: 0,\n    paddingRight: 0,\n    paddingTop: 6,\n    paddingBottom: 6,\n    height: 37,\n    display: \"inline-flex\",\n    marginBottom: 0,\n    marginTop: 0,\n    alignItems: \"center\"\n};\nvar button = {\n    ...playerButtonStyle,\n    position: \"relative\"\n};\nvar PlaybackrateControl = ({ playbackRates, canvasSize })=>{\n    const { ref, isComponentVisible, setIsComponentVisible } = useComponentVisible(false);\n    const { playbackRate } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.Timeline.TimelineContext);\n    const onClick = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        e.stopPropagation();\n        e.preventDefault();\n        setIsComponentVisible((prevIsComponentVisible)=>!prevIsComponentVisible);\n    }, [\n        setIsComponentVisible\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        ref,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"button\", {\n            type: \"button\",\n            \"aria-label\": \"Change playback rate\",\n            style: button,\n            onClick,\n            children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                    style: label,\n                    children: [\n                        playbackRate,\n                        \"x\"\n                    ]\n                }),\n                isComponentVisible && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PlaybackPopup, {\n                    canvasSize,\n                    playbackRates,\n                    setIsComponentVisible\n                })\n            ]\n        })\n    });\n};\n// src/PlayerSeekBar.tsx\n\n\n\nvar getFrameFromX = (clientX, durationInFrames, width)=>{\n    const pos = clientX;\n    const frame = Math.round((0,remotion__WEBPACK_IMPORTED_MODULE_1__.interpolate)(pos, [\n        0,\n        width\n    ], [\n        0,\n        durationInFrames - 1\n    ], {\n        extrapolateLeft: \"clamp\",\n        extrapolateRight: \"clamp\"\n    }));\n    return frame;\n};\nvar BAR_HEIGHT2 = 5;\nvar KNOB_SIZE2 = 12;\nvar VERTICAL_PADDING = 4;\nvar containerStyle = {\n    userSelect: \"none\",\n    WebkitUserSelect: \"none\",\n    paddingTop: VERTICAL_PADDING,\n    paddingBottom: VERTICAL_PADDING,\n    boxSizing: \"border-box\",\n    cursor: \"pointer\",\n    position: \"relative\",\n    touchAction: \"none\"\n};\nvar barBackground = {\n    height: BAR_HEIGHT2,\n    backgroundColor: \"rgba(255, 255, 255, 0.25)\",\n    width: \"100%\",\n    borderRadius: BAR_HEIGHT2 / 2\n};\nvar findBodyInWhichDivIsLocated = (div)=>{\n    let current = div;\n    while(current.parentElement){\n        current = current.parentElement;\n    }\n    return current;\n};\nvar PlayerSeekBar = ({ durationInFrames, onSeekEnd, onSeekStart, inFrame, outFrame })=>{\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const barHovered = useHoverState(containerRef, false);\n    const size = useElementSize(containerRef, {\n        triggerOnWindowResize: true,\n        shouldApplyCssTransforms: true\n    });\n    const { seek, play, pause, playing } = usePlayer();\n    const frame = remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.Timeline.useTimelinePosition();\n    const [dragging, setDragging] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        dragging: false\n    });\n    const width = size?.width ?? 0;\n    const onPointerDown = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        if (e.button !== 0) {\n            return;\n        }\n        const posLeft = containerRef.current?.getBoundingClientRect().left;\n        const _frame = getFrameFromX(e.clientX - posLeft, durationInFrames, width);\n        pause();\n        seek(_frame);\n        setDragging({\n            dragging: true,\n            wasPlaying: playing\n        });\n        onSeekStart();\n    }, [\n        durationInFrames,\n        width,\n        pause,\n        seek,\n        playing,\n        onSeekStart\n    ]);\n    const onPointerMove = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        if (!size) {\n            throw new Error(\"Player has no size\");\n        }\n        if (!dragging.dragging) {\n            return;\n        }\n        const posLeft = containerRef.current?.getBoundingClientRect().left;\n        const _frame = getFrameFromX(e.clientX - posLeft, durationInFrames, size.width);\n        seek(_frame);\n    }, [\n        dragging.dragging,\n        durationInFrames,\n        seek,\n        size\n    ]);\n    const onPointerUp = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        setDragging({\n            dragging: false\n        });\n        if (!dragging.dragging) {\n            return;\n        }\n        if (dragging.wasPlaying) {\n            play();\n        } else {\n            pause();\n        }\n        onSeekEnd();\n    }, [\n        dragging,\n        onSeekEnd,\n        pause,\n        play\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!dragging.dragging) {\n            return;\n        }\n        const body = findBodyInWhichDivIsLocated(containerRef.current);\n        body.addEventListener(\"pointermove\", onPointerMove);\n        body.addEventListener(\"pointerup\", onPointerUp);\n        return ()=>{\n            body.removeEventListener(\"pointermove\", onPointerMove);\n            body.removeEventListener(\"pointerup\", onPointerUp);\n        };\n    }, [\n        dragging.dragging,\n        onPointerMove,\n        onPointerUp\n    ]);\n    const knobStyle = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            height: KNOB_SIZE2,\n            width: KNOB_SIZE2,\n            borderRadius: KNOB_SIZE2 / 2,\n            position: \"absolute\",\n            top: VERTICAL_PADDING - KNOB_SIZE2 / 2 + 5 / 2,\n            backgroundColor: \"white\",\n            left: Math.max(0, frame / Math.max(1, durationInFrames - 1) * width - KNOB_SIZE2 / 2),\n            boxShadow: \"0 0 2px black\",\n            opacity: Number(barHovered || dragging.dragging)\n        };\n    }, [\n        barHovered,\n        dragging.dragging,\n        durationInFrames,\n        frame,\n        width\n    ]);\n    const fillStyle = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            height: BAR_HEIGHT2,\n            backgroundColor: \"rgba(255, 255, 255, 1)\",\n            width: (frame - (inFrame ?? 0)) / (durationInFrames - 1) * width,\n            marginLeft: (inFrame ?? 0) / (durationInFrames - 1) * width,\n            borderRadius: BAR_HEIGHT2 / 2\n        };\n    }, [\n        durationInFrames,\n        frame,\n        inFrame,\n        width\n    ]);\n    const active = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            height: BAR_HEIGHT2,\n            backgroundColor: \"rgba(255, 255, 255, 0.25)\",\n            width: ((outFrame ?? durationInFrames - 1) - (inFrame ?? 0)) / (durationInFrames - 1) * 100 + \"%\",\n            marginLeft: (inFrame ?? 0) / (durationInFrames - 1) * 100 + \"%\",\n            borderRadius: BAR_HEIGHT2 / 2,\n            position: \"absolute\"\n        };\n    }, [\n        durationInFrames,\n        inFrame,\n        outFrame\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        ref: containerRef,\n        onPointerDown,\n        style: containerStyle,\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                style: barBackground,\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        style: active\n                    }),\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        style: fillStyle\n                    })\n                ]\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                style: knobStyle\n            })\n        ]\n    });\n};\n// src/PlayerTimeLabel.tsx\n\n\n// src/format-time.ts\nvar formatTime = (timeInSeconds)=>{\n    const minutes = Math.floor(timeInSeconds / 60);\n    const seconds = Math.floor(timeInSeconds - minutes * 60);\n    return `${String(minutes)}:${String(seconds).padStart(2, \"0\")}`;\n};\n// src/PlayerTimeLabel.tsx\n\nvar PlayerTimeLabel = ({ durationInFrames, maxTimeLabelWidth, fps })=>{\n    const frame = remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.Timeline.useTimelinePosition();\n    const timeLabel = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            color: \"white\",\n            fontFamily: \"sans-serif\",\n            fontSize: 14,\n            maxWidth: maxTimeLabelWidth === null ? undefined : maxTimeLabelWidth,\n            overflow: \"hidden\",\n            textOverflow: \"ellipsis\"\n        };\n    }, [\n        maxTimeLabelWidth\n    ]);\n    const isLastFrame = frame === durationInFrames - 1;\n    const frameToDisplay = isLastFrame ? frame + 1 : frame;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        style: timeLabel,\n        children: [\n            formatTime(frameToDisplay / fps),\n            \" / \",\n            formatTime(durationInFrames / fps)\n        ]\n    });\n};\n// src/use-video-controls-resize.ts\n\nvar X_SPACER = 10;\nvar X_PADDING = 12;\nvar useVideoControlsResize = ({ allowFullscreen: allowFullScreen, playerWidth })=>{\n    const resizeInfo = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        const playPauseIconSize = ICON_SIZE;\n        const volumeIconSize = ICON_SIZE;\n        const _fullscreenIconSize = allowFullScreen ? fullscreenIconSize : 0;\n        const elementsSize = volumeIconSize + playPauseIconSize + _fullscreenIconSize + X_PADDING * 2 + X_SPACER * 2;\n        const maxTimeLabelWidth = playerWidth - elementsSize;\n        const maxTimeLabelWidthWithoutNegativeValue = Math.max(maxTimeLabelWidth, 0);\n        const availableTimeLabelWidthIfVolumeOpen = maxTimeLabelWidthWithoutNegativeValue - VOLUME_SLIDER_WIDTH;\n        const computedLabelWidth = availableTimeLabelWidthIfVolumeOpen < VOLUME_SLIDER_WIDTH ? maxTimeLabelWidthWithoutNegativeValue : availableTimeLabelWidthIfVolumeOpen;\n        const minWidthForHorizontalDisplay = computedLabelWidth + elementsSize + VOLUME_SLIDER_WIDTH;\n        const displayVerticalVolumeSlider = playerWidth < minWidthForHorizontalDisplay;\n        return {\n            maxTimeLabelWidth: maxTimeLabelWidthWithoutNegativeValue === 0 ? null : maxTimeLabelWidthWithoutNegativeValue,\n            displayVerticalVolumeSlider\n        };\n    }, [\n        allowFullScreen,\n        playerWidth\n    ]);\n    return resizeInfo;\n};\n// src/PlayerControls.tsx\n\nvar gradientSteps = [\n    0,\n    0.013,\n    0.049,\n    0.104,\n    0.175,\n    0.259,\n    0.352,\n    0.45,\n    0.55,\n    0.648,\n    0.741,\n    0.825,\n    0.896,\n    0.951,\n    0.987\n];\nvar gradientOpacities = [\n    0,\n    8.1,\n    15.5,\n    22.5,\n    29,\n    35.3,\n    41.2,\n    47.1,\n    52.9,\n    58.8,\n    64.7,\n    71,\n    77.5,\n    84.5,\n    91.9\n];\nvar globalGradientOpacity = 1 / 0.7;\nvar containerStyle2 = {\n    boxSizing: \"border-box\",\n    position: \"absolute\",\n    bottom: 0,\n    width: \"100%\",\n    paddingTop: 40,\n    paddingBottom: 10,\n    backgroundImage: `linear-gradient(to bottom,${gradientSteps.map((g, i)=>{\n        return `hsla(0, 0%, 0%, ${g}) ${gradientOpacities[i] * globalGradientOpacity}%`;\n    }).join(\", \")}, hsl(0, 0%, 0%) 100%)`,\n    backgroundSize: \"auto 145px\",\n    display: \"flex\",\n    paddingRight: X_PADDING,\n    paddingLeft: X_PADDING,\n    flexDirection: \"column\",\n    transition: \"opacity 0.3s\"\n};\nvar controlsRow = {\n    display: \"flex\",\n    flexDirection: \"row\",\n    width: \"100%\",\n    alignItems: \"center\",\n    justifyContent: \"center\",\n    userSelect: \"none\",\n    WebkitUserSelect: \"none\"\n};\nvar leftPartStyle = {\n    display: \"flex\",\n    flexDirection: \"row\",\n    userSelect: \"none\",\n    WebkitUserSelect: \"none\",\n    alignItems: \"center\"\n};\nvar xSpacer = {\n    width: 12\n};\nvar ySpacer = {\n    height: 8\n};\nvar flex1 = {\n    flex: 1\n};\nvar fullscreen = {};\nvar Controls = ({ durationInFrames, isFullscreen, fps, showVolumeControls, onFullscreenButtonClick, allowFullscreen, onExitFullscreenButtonClick, spaceKeyToPlayOrPause, onSeekEnd, onSeekStart, inFrame, outFrame, initiallyShowControls, canvasSize, renderPlayPauseButton, renderFullscreenButton, alwaysShowControls, showPlaybackRateControl, containerRef, buffering, hideControlsWhenPointerDoesntMove, onPointerDown, onDoubleClick, renderMuteButton, renderVolumeSlider, playing, toggle })=>{\n    const playButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const [supportsFullscreen, setSupportsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const hovered = useHoverState(containerRef, hideControlsWhenPointerDoesntMove);\n    const { maxTimeLabelWidth, displayVerticalVolumeSlider } = useVideoControlsResize({\n        allowFullscreen,\n        playerWidth: canvasSize?.width ?? 0\n    });\n    const [shouldShowInitially, setInitiallyShowControls] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(()=>{\n        if (typeof initiallyShowControls === \"boolean\") {\n            return initiallyShowControls;\n        }\n        if (typeof initiallyShowControls === \"number\") {\n            if (initiallyShowControls % 1 !== 0) {\n                throw new Error(\"initiallyShowControls must be an integer or a boolean\");\n            }\n            if (Number.isNaN(initiallyShowControls)) {\n                throw new Error(\"initiallyShowControls must not be NaN\");\n            }\n            if (!Number.isFinite(initiallyShowControls)) {\n                throw new Error(\"initiallyShowControls must be finite\");\n            }\n            if (initiallyShowControls <= 0) {\n                throw new Error(\"initiallyShowControls must be a positive integer\");\n            }\n            return initiallyShowControls;\n        }\n        throw new TypeError(\"initiallyShowControls must be a number or a boolean\");\n    });\n    const containerCss = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        const shouldShow = hovered || !playing || shouldShowInitially || alwaysShowControls;\n        return {\n            ...containerStyle2,\n            opacity: Number(shouldShow)\n        };\n    }, [\n        hovered,\n        shouldShowInitially,\n        playing,\n        alwaysShowControls\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (playButtonRef.current && spaceKeyToPlayOrPause) {\n            playButtonRef.current.focus({\n                preventScroll: true\n            });\n        }\n    }, [\n        playing,\n        spaceKeyToPlayOrPause\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setSupportsFullscreen((typeof document !== \"undefined\" && (document.fullscreenEnabled || document.webkitFullscreenEnabled)) ?? false);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (shouldShowInitially === false) {\n            return;\n        }\n        const time = shouldShowInitially === true ? 2000 : shouldShowInitially;\n        const timeout = setTimeout(()=>{\n            setInitiallyShowControls(false);\n        }, time);\n        return ()=>{\n            clearInterval(timeout);\n        };\n    }, [\n        shouldShowInitially\n    ]);\n    const playbackRates = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (showPlaybackRateControl === true) {\n            return [\n                0.5,\n                0.8,\n                1,\n                1.2,\n                1.5,\n                1.8,\n                2,\n                2.5,\n                3\n            ];\n        }\n        if (Array.isArray(showPlaybackRateControl)) {\n            for (const rate of showPlaybackRateControl){\n                if (typeof rate !== \"number\") {\n                    throw new Error(\"Every item in showPlaybackRateControl must be a number\");\n                }\n                if (rate <= 0) {\n                    throw new Error(\"Every item in showPlaybackRateControl must be positive\");\n                }\n            }\n            return showPlaybackRateControl;\n        }\n        return null;\n    }, [\n        showPlaybackRateControl\n    ]);\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const flexRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const onPointerDownIfContainer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        if (e.target === ref.current || e.target === flexRef.current) {\n            onPointerDown?.(e);\n        }\n    }, [\n        onPointerDown\n    ]);\n    const onDoubleClickIfContainer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        if (e.target === ref.current || e.target === flexRef.current) {\n            onDoubleClick?.(e);\n        }\n    }, [\n        onDoubleClick\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        ref,\n        style: containerCss,\n        onPointerDown: onPointerDownIfContainer,\n        onDoubleClick: onDoubleClickIfContainer,\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                ref: flexRef,\n                style: controlsRow,\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                        style: leftPartStyle,\n                        children: [\n                            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"button\", {\n                                ref: playButtonRef,\n                                type: \"button\",\n                                style: playerButtonStyle,\n                                onClick: toggle,\n                                \"aria-label\": playing ? \"Pause video\" : \"Play video\",\n                                title: playing ? \"Pause video\" : \"Play video\",\n                                children: renderPlayPauseButton === null ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DefaultPlayPauseButton, {\n                                    buffering,\n                                    playing\n                                }) : renderPlayPauseButton({\n                                    playing,\n                                    isBuffering: buffering\n                                }) ?? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(DefaultPlayPauseButton, {\n                                    buffering,\n                                    playing\n                                })\n                            }),\n                            showVolumeControls ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                                        style: xSpacer\n                                    }),\n                                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(MediaVolumeSlider, {\n                                        renderMuteButton,\n                                        renderVolumeSlider,\n                                        displayVerticalVolumeSlider\n                                    })\n                                ]\n                            }) : null,\n                            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                                style: xSpacer\n                            }),\n                            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PlayerTimeLabel, {\n                                durationInFrames,\n                                fps,\n                                maxTimeLabelWidth\n                            }),\n                            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                                style: xSpacer\n                            })\n                        ]\n                    }),\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        style: flex1\n                    }),\n                    playbackRates && canvasSize && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PlaybackrateControl, {\n                        canvasSize,\n                        playbackRates\n                    }),\n                    playbackRates && supportsFullscreen && allowFullscreen ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        style: xSpacer\n                    }) : null,\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                        style: fullscreen,\n                        children: supportsFullscreen && allowFullscreen ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"button\", {\n                            type: \"button\",\n                            \"aria-label\": isFullscreen ? \"Exit fullscreen\" : \"Enter Fullscreen\",\n                            title: isFullscreen ? \"Exit fullscreen\" : \"Enter Fullscreen\",\n                            style: playerButtonStyle,\n                            onClick: isFullscreen ? onExitFullscreenButtonClick : onFullscreenButtonClick,\n                            children: renderFullscreenButton === null ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(FullscreenIcon, {\n                                isFullscreen\n                            }) : renderFullscreenButton({\n                                isFullscreen\n                            })\n                        }) : null\n                    })\n                ]\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                style: ySpacer\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PlayerSeekBar, {\n                onSeekEnd,\n                onSeekStart,\n                durationInFrames,\n                inFrame,\n                outFrame\n            })\n        ]\n    });\n};\n// src/error-boundary.tsx\n\n\nvar errorStyle = {\n    display: \"flex\",\n    justifyContent: \"center\",\n    alignItems: \"center\",\n    flex: 1,\n    height: \"100%\",\n    width: \"100%\"\n};\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_2__.Component {\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: error\n        };\n    }\n    componentDidCatch(error) {\n        this.props.onError(error);\n    }\n    render() {\n        if (this.state.hasError) {\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                style: errorStyle,\n                children: this.props.errorFallback({\n                    error: this.state.hasError\n                })\n            });\n        }\n        return this.props.children;\n    }\n    constructor(...args){\n        super(...args);\n        this.state = {\n            hasError: null\n        };\n    }\n}\n// src/license-blacklist.tsx\n\n\nvar getHashOfDomain = async ()=>{\n    if (true) {\n        return null;\n    }\n    if (typeof window.crypto === \"undefined\") {\n        return null;\n    }\n    if (typeof window.crypto.subtle === \"undefined\") {\n        return null;\n    }\n    try {\n        const hashBuffer = await crypto.subtle.digest(\"SHA-256\", new TextEncoder().encode(window.location.hostname));\n        return Array.from(new Uint8Array(hashBuffer)).map((b)=>b.toString(16).padStart(2, \"0\")).join(\"\");\n    } catch  {\n        return null;\n    }\n};\nvar style = {\n    backgroundColor: \"red\",\n    position: \"absolute\",\n    padding: 12,\n    fontFamily: \"Arial\"\n};\nvar DOMAIN_BLACKLIST = [\n    \"28d262b44cc61fa750f1686b16ad0604dabfe193fbc263eec05c89b7ad4c2cd6\",\n    \"4db1b0a94be33165dfefcb3ba03d04c7a2666dd27c496d3dc9fa41858e94925e\",\n    \"fbc48530bbf245da790f63675e84e06bab38c3b114fab07eb350025119922bdc\",\n    \"7baf10a8932757b1b3a22b3fce10a048747ac2f8eaf638603487e3705b07eb83\",\n    \"8a6c21a598d8c667272b5207c051b85997bf5b45d5fb712378be3f27cd72c6a6\",\n    \"a2f7aaac9c50a9255e7fc376110c4e0bfe153722dc66ed3c5d3bf2a135f65518\"\n];\nvar ran = false;\nvar RenderWarningIfBlacklist = ()=>{\n    const [unlicensed, setUnlicensed] = react__WEBPACK_IMPORTED_MODULE_2__.useState(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (ran) {\n            return;\n        }\n        ran = true;\n        getHashOfDomain().then((hash)=>{\n            if (hash && DOMAIN_BLACKLIST.includes(hash)) {\n                setUnlicensed(true);\n            }\n        }).catch(()=>{});\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!unlicensed) {\n            return;\n        }\n        const ensureBanner = ()=>{\n            const banner = document.querySelector(\".warning-banner\");\n            if (!banner) {\n                const div = document.createElement(\"div\");\n                div.className = \"warning-banner\";\n                Object.assign(div.style, style, {\n                    zIndex: \"9999\",\n                    cssText: `${style.cssText} !important;`\n                });\n                div.innerHTML = `\n\t        <a href=\"https://github.com/remotion-dev/remotion/pull/4589\" style=\"color: white;\">\n\t          Remotion Unlicensed – Contact <EMAIL>\n\t        </a>\n\t      `;\n                document.body.appendChild(div);\n            }\n        };\n        const observer = new MutationObserver(()=>ensureBanner());\n        observer.observe(document.body, {\n            childList: true,\n            subtree: true\n        });\n        return ()=>{\n            observer.disconnect();\n        };\n    }, [\n        unlicensed\n    ]);\n    if (!unlicensed) {\n        return null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        style,\n        className: \"warning-banner\",\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"a\", {\n            style: {\n                color: \"white\"\n            },\n            href: \"https://github.com/remotion-dev/remotion/pull/4589\",\n            children: \"Remotion Unlicensed – Contact <EMAIL>\"\n        })\n    });\n};\n// src/player-css-classname.ts\nvar playerCssClassname = (override)=>{\n    return override ?? \"__remotion-player\";\n};\n// src/utils/is-node.ts\nvar IS_NODE = typeof document === \"undefined\";\n// src/utils/use-click-prevention-on-double-click.ts\n\n// src/utils/cancellable-promise.ts\nvar cancellablePromise = (promise)=>{\n    let isCanceled = false;\n    const wrappedPromise = new Promise((resolve, reject)=>{\n        promise.then((value)=>{\n            if (isCanceled) {\n                reject({\n                    isCanceled,\n                    value\n                });\n                return;\n            }\n            resolve(value);\n        }).catch((error)=>{\n            reject({\n                isCanceled,\n                error\n            });\n        });\n    });\n    return {\n        promise: wrappedPromise,\n        cancel: ()=>{\n            isCanceled = true;\n        }\n    };\n};\n// src/utils/delay.ts\nvar delay = (n)=>new Promise((resolve)=>setTimeout(resolve, n));\n// src/utils/use-cancellable-promises.ts\n\nvar useCancellablePromises = ()=>{\n    const pendingPromises = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)([]);\n    const appendPendingPromise = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((promise)=>{\n        pendingPromises.current = [\n            ...pendingPromises.current,\n            promise\n        ];\n    }, []);\n    const removePendingPromise = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((promise)=>{\n        pendingPromises.current = pendingPromises.current.filter((p)=>p !== promise);\n    }, []);\n    const clearPendingPromises = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>pendingPromises.current.map((p)=>p.cancel()), []);\n    const api = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>({\n            appendPendingPromise,\n            removePendingPromise,\n            clearPendingPromises\n        }), [\n        appendPendingPromise,\n        clearPendingPromises,\n        removePendingPromise\n    ]);\n    return api;\n};\n// src/utils/use-click-prevention-on-double-click.ts\nvar useClickPreventionOnDoubleClick = (onClick, onDoubleClick, doubleClickToFullscreen)=>{\n    const api = useCancellablePromises();\n    const handleClick = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(async (e)=>{\n        if (e instanceof PointerEvent ? e.pointerType === \"touch\" : e.nativeEvent.pointerType === \"touch\") {\n            onClick(e);\n            return;\n        }\n        api.clearPendingPromises();\n        const waitForClick = cancellablePromise(delay(200));\n        api.appendPendingPromise(waitForClick);\n        try {\n            await waitForClick.promise;\n            api.removePendingPromise(waitForClick);\n            onClick(e);\n        } catch (errorInfo) {\n            const info = errorInfo;\n            api.removePendingPromise(waitForClick);\n            if (!info.isCanceled) {\n                throw info.error;\n            }\n        }\n    }, [\n        api,\n        onClick\n    ]);\n    const handlePointerDown = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        document.addEventListener(\"pointerup\", (newEvt)=>{\n            handleClick(newEvt);\n        }, {\n            once: true\n        });\n    }, [\n        handleClick\n    ]);\n    const handleDoubleClick = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        api.clearPendingPromises();\n        onDoubleClick();\n    }, [\n        api,\n        onDoubleClick\n    ]);\n    const returnValue = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (!doubleClickToFullscreen) {\n            return {\n                handlePointerDown: onClick,\n                handleDoubleClick: ()=>{\n                    return;\n                }\n            };\n        }\n        return {\n            handlePointerDown,\n            handleDoubleClick\n        };\n    }, [\n        doubleClickToFullscreen,\n        handleDoubleClick,\n        handlePointerDown,\n        onClick\n    ]);\n    return returnValue;\n};\n// src/PlayerUI.tsx\n\nvar reactVersion = react__WEBPACK_IMPORTED_MODULE_2__.version.split(\".\")[0];\nif (reactVersion === \"0\") {\n    throw new Error(`Version ${reactVersion} of \"react\" is not supported by Remotion`);\n}\nvar doesReactVersionSupportSuspense = parseInt(reactVersion, 10) >= 18;\nvar PlayerUI = ({ controls, style: style2, loop, autoPlay, allowFullscreen, inputProps, clickToPlay, showVolumeControls, doubleClickToFullscreen, spaceKeyToPlayOrPause, errorFallback, playbackRate, renderLoading, renderPoster, className: className2, moveToBeginningWhenEnded, showPosterWhenUnplayed, showPosterWhenEnded, showPosterWhenPaused, showPosterWhenBuffering, showPosterWhenBufferingAndPaused, inFrame, outFrame, initiallyShowControls, renderFullscreen: renderFullscreenButton, renderPlayPauseButton, renderMuteButton, renderVolumeSlider, alwaysShowControls, showPlaybackRateControl, posterFillMode, bufferStateDelayInMilliseconds, hideControlsWhenPointerDoesntMove, overflowVisible, browserMediaControlsBehavior, overrideInternalClassName, noSuspense }, ref)=>{\n    const config = remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.useUnsafeVideoConfig();\n    const video = remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.useVideo();\n    const container = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const canvasSize = useElementSize(container, {\n        triggerOnWindowResize: false,\n        shouldApplyCssTransforms: false\n    });\n    const [hasPausedToResume, setHasPausedToResume] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [shouldAutoplay, setShouldAutoPlay] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(autoPlay);\n    const [isFullscreen, setIsFullscreen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(()=>false);\n    const [seeking, setSeeking] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const supportsFullScreen = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (typeof document === \"undefined\") {\n            return false;\n        }\n        return Boolean(document.fullscreenEnabled || document.webkitFullscreenEnabled);\n    }, []);\n    const player = usePlayer();\n    const playerToggle = player.toggle;\n    usePlayback({\n        loop,\n        playbackRate,\n        moveToBeginningWhenEnded,\n        inFrame,\n        outFrame,\n        getCurrentFrame: player.getCurrentFrame,\n        browserMediaControlsBehavior\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (hasPausedToResume && !player.playing) {\n            setHasPausedToResume(false);\n            player.play();\n        }\n    }, [\n        hasPausedToResume,\n        player\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const { current } = container;\n        if (!current) {\n            return;\n        }\n        const onFullscreenChange = ()=>{\n            const newValue = document.fullscreenElement === current || document.webkitFullscreenElement === current;\n            setIsFullscreen(newValue);\n        };\n        document.addEventListener(\"fullscreenchange\", onFullscreenChange);\n        document.addEventListener(\"webkitfullscreenchange\", onFullscreenChange);\n        return ()=>{\n            document.removeEventListener(\"fullscreenchange\", onFullscreenChange);\n            document.removeEventListener(\"webkitfullscreenchange\", onFullscreenChange);\n        };\n    }, []);\n    const toggle = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        playerToggle(e);\n    }, [\n        playerToggle\n    ]);\n    const requestFullscreen = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        if (!allowFullscreen) {\n            throw new Error(\"allowFullscreen is false\");\n        }\n        if (!supportsFullScreen) {\n            throw new Error(\"Browser doesnt support fullscreen\");\n        }\n        if (!container.current) {\n            throw new Error(\"No player ref found\");\n        }\n        if (container.current.webkitRequestFullScreen) {\n            container.current.webkitRequestFullScreen();\n        } else {\n            container.current.requestFullscreen();\n        }\n    }, [\n        allowFullscreen,\n        supportsFullScreen\n    ]);\n    const exitFullscreen = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        if (document.webkitExitFullscreen) {\n            document.webkitExitFullscreen();\n        } else {\n            document.exitFullscreen();\n        }\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const { current } = container;\n        if (!current) {\n            return;\n        }\n        const fullscreenChange = ()=>{\n            const element = document.webkitFullscreenElement ?? document.fullscreenElement;\n            if (element && element === container.current) {\n                player.emitter.dispatchFullscreenChange({\n                    isFullscreen: true\n                });\n            } else {\n                player.emitter.dispatchFullscreenChange({\n                    isFullscreen: false\n                });\n            }\n        };\n        current.addEventListener(\"webkitfullscreenchange\", fullscreenChange);\n        current.addEventListener(\"fullscreenchange\", fullscreenChange);\n        return ()=>{\n            current.removeEventListener(\"webkitfullscreenchange\", fullscreenChange);\n            current.removeEventListener(\"fullscreenchange\", fullscreenChange);\n        };\n    }, [\n        player.emitter\n    ]);\n    const durationInFrames = config?.durationInFrames ?? 1;\n    const layout = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (!config || !canvasSize) {\n            return null;\n        }\n        return calculateCanvasTransformation({\n            canvasSize,\n            compositionHeight: config.height,\n            compositionWidth: config.width,\n            previewSize: \"auto\"\n        });\n    }, [\n        canvasSize,\n        config\n    ]);\n    const scale = layout?.scale ?? 1;\n    const initialScaleIgnored = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!initialScaleIgnored.current) {\n            initialScaleIgnored.current = true;\n            return;\n        }\n        player.emitter.dispatchScaleChange(scale);\n    }, [\n        player.emitter,\n        scale\n    ]);\n    const { setMediaVolume, setMediaMuted } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.SetMediaVolumeContext);\n    const { mediaMuted, mediaVolume } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.MediaVolumeContext);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        player.emitter.dispatchVolumeChange(mediaVolume);\n    }, [\n        player.emitter,\n        mediaVolume\n    ]);\n    const isMuted = mediaMuted || mediaVolume === 0;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        player.emitter.dispatchMuteChange({\n            isMuted\n        });\n    }, [\n        player.emitter,\n        isMuted\n    ]);\n    const [showBufferIndicator, setShowBufferState] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        let timeout = null;\n        let stopped = false;\n        const onBuffer = ()=>{\n            stopped = false;\n            requestAnimationFrame(()=>{\n                if (bufferStateDelayInMilliseconds === 0) {\n                    setShowBufferState(true);\n                } else {\n                    timeout = setTimeout(()=>{\n                        if (!stopped) {\n                            setShowBufferState(true);\n                        }\n                    }, bufferStateDelayInMilliseconds);\n                }\n            });\n        };\n        const onResume = ()=>{\n            requestAnimationFrame(()=>{\n                stopped = true;\n                setShowBufferState(false);\n                if (timeout) {\n                    clearTimeout(timeout);\n                }\n            });\n        };\n        player.emitter.addEventListener(\"waiting\", onBuffer);\n        player.emitter.addEventListener(\"resume\", onResume);\n        return ()=>{\n            player.emitter.removeEventListener(\"waiting\", onBuffer);\n            player.emitter.removeEventListener(\"resume\", onResume);\n            setShowBufferState(false);\n            if (timeout) {\n                clearTimeout(timeout);\n            }\n            stopped = true;\n        };\n    }, [\n        bufferStateDelayInMilliseconds,\n        player.emitter\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, ()=>{\n        const methods = {\n            play: player.play,\n            pause: ()=>{\n                setHasPausedToResume(false);\n                player.pause();\n            },\n            toggle,\n            getContainerNode: ()=>container.current,\n            getCurrentFrame: player.getCurrentFrame,\n            isPlaying: player.isPlaying,\n            seekTo: (f)=>{\n                const lastFrame = durationInFrames - 1;\n                const frameToSeekTo = Math.max(0, Math.min(lastFrame, f));\n                if (player.isPlaying()) {\n                    const pauseToResume = frameToSeekTo !== lastFrame || loop;\n                    setHasPausedToResume(pauseToResume);\n                    player.pause();\n                }\n                if (frameToSeekTo === lastFrame && !loop) {\n                    player.emitter.dispatchEnded();\n                }\n                player.seek(frameToSeekTo);\n            },\n            isFullscreen: ()=>{\n                const { current } = container;\n                if (!current) {\n                    return false;\n                }\n                return document.fullscreenElement === current || document.webkitFullscreenElement === current;\n            },\n            requestFullscreen,\n            exitFullscreen,\n            getVolume: ()=>{\n                if (mediaMuted) {\n                    return 0;\n                }\n                return mediaVolume;\n            },\n            setVolume: (vol)=>{\n                if (typeof vol !== \"number\") {\n                    throw new TypeError(`setVolume() takes a number, got value of type ${typeof vol}`);\n                }\n                if (isNaN(vol)) {\n                    throw new TypeError(`setVolume() got a number that is NaN. Volume must be between 0 and 1.`);\n                }\n                if (vol < 0 || vol > 1) {\n                    throw new TypeError(`setVolume() got a number that is out of range. Must be between 0 and 1, got ${typeof vol}`);\n                }\n                setMediaVolume(vol);\n            },\n            isMuted: ()=>isMuted,\n            mute: ()=>{\n                setMediaMuted(true);\n            },\n            unmute: ()=>{\n                setMediaMuted(false);\n            },\n            getScale: ()=>scale,\n            pauseAndReturnToPlayStart: ()=>{\n                player.pauseAndReturnToPlayStart();\n            }\n        };\n        return Object.assign(player.emitter, methods);\n    }, [\n        durationInFrames,\n        exitFullscreen,\n        loop,\n        mediaMuted,\n        isMuted,\n        mediaVolume,\n        player,\n        requestFullscreen,\n        setMediaMuted,\n        setMediaVolume,\n        toggle,\n        scale\n    ]);\n    const VideoComponent = video ? video.component : null;\n    const outerStyle = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return calculateOuterStyle({\n            canvasSize,\n            config,\n            style: style2,\n            overflowVisible,\n            layout\n        });\n    }, [\n        canvasSize,\n        config,\n        layout,\n        overflowVisible,\n        style2\n    ]);\n    const outer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return calculateOuter({\n            config,\n            layout,\n            scale,\n            overflowVisible\n        });\n    }, [\n        config,\n        layout,\n        overflowVisible,\n        scale\n    ]);\n    const containerStyle3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return calculateContainerStyle({\n            config,\n            layout,\n            scale,\n            overflowVisible\n        });\n    }, [\n        config,\n        layout,\n        overflowVisible,\n        scale\n    ]);\n    const playerPause = player.pause;\n    const playerDispatchError = player.emitter.dispatchError;\n    const onError = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((error)=>{\n        playerPause();\n        playerDispatchError(error);\n    }, [\n        playerDispatchError,\n        playerPause\n    ]);\n    const onFullscreenButtonClick = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        e.stopPropagation();\n        requestFullscreen();\n    }, [\n        requestFullscreen\n    ]);\n    const onExitFullscreenButtonClick = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        e.stopPropagation();\n        exitFullscreen();\n    }, [\n        exitFullscreen\n    ]);\n    const onSingleClick = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((e)=>{\n        const rightClick = e instanceof MouseEvent ? e.button === 2 : e.nativeEvent.button;\n        if (rightClick) {\n            return;\n        }\n        toggle(e);\n    }, [\n        toggle\n    ]);\n    const onSeekStart = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        setSeeking(true);\n    }, []);\n    const onSeekEnd = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        setSeeking(false);\n    }, []);\n    const onDoubleClick = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        if (isFullscreen) {\n            exitFullscreen();\n        } else {\n            requestFullscreen();\n        }\n    }, [\n        exitFullscreen,\n        isFullscreen,\n        requestFullscreen\n    ]);\n    const { handlePointerDown, handleDoubleClick } = useClickPreventionOnDoubleClick(onSingleClick, onDoubleClick, doubleClickToFullscreen && allowFullscreen && supportsFullScreen);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (shouldAutoplay) {\n            player.play();\n            setShouldAutoPlay(false);\n        }\n    }, [\n        shouldAutoplay,\n        player\n    ]);\n    const loadingMarkup = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return renderLoading ? renderLoading({\n            height: outerStyle.height,\n            width: outerStyle.width,\n            isBuffering: showBufferIndicator\n        }) : null;\n    }, [\n        outerStyle.height,\n        outerStyle.width,\n        renderLoading,\n        showBufferIndicator\n    ]);\n    const currentScale = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            type: \"scale\",\n            scale\n        };\n    }, [\n        scale\n    ]);\n    if (!config) {\n        return null;\n    }\n    const poster = renderPoster ? renderPoster({\n        height: posterFillMode === \"player-size\" ? outerStyle.height : config.height,\n        width: posterFillMode === \"player-size\" ? outerStyle.width : config.width,\n        isBuffering: showBufferIndicator\n    }) : null;\n    if (poster === undefined) {\n        throw new TypeError(\"renderPoster() must return a React element, but undefined was returned\");\n    }\n    const shouldShowPoster = poster && [\n        showPosterWhenPaused && !player.isPlaying() && !seeking,\n        showPosterWhenEnded && player.isLastFrame && !player.isPlaying(),\n        showPosterWhenUnplayed && !player.hasPlayed && !player.isPlaying(),\n        showPosterWhenBuffering && showBufferIndicator && player.isPlaying(),\n        showPosterWhenBufferingAndPaused && showBufferIndicator && !player.isPlaying()\n    ].some(Boolean);\n    const { left, top, width, height, ...outerWithoutScale } = outer;\n    const content = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                style: outer,\n                onPointerDown: clickToPlay ? handlePointerDown : undefined,\n                onDoubleClick: doubleClickToFullscreen ? handleDoubleClick : undefined,\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                        style: containerStyle3,\n                        className: playerCssClassname(overrideInternalClassName),\n                        children: [\n                            VideoComponent ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ErrorBoundary, {\n                                onError,\n                                errorFallback,\n                                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.CurrentScaleContext.Provider, {\n                                    value: currentScale,\n                                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(VideoComponent, {\n                                        ...video?.props ?? {},\n                                        ...inputProps ?? {}\n                                    })\n                                })\n                            }) : null,\n                            shouldShowPoster && posterFillMode === \"composition-size\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                                style: {\n                                    ...outerWithoutScale,\n                                    width: config.width,\n                                    height: config.height\n                                },\n                                onPointerDown: clickToPlay ? handlePointerDown : undefined,\n                                onDoubleClick: doubleClickToFullscreen ? handleDoubleClick : undefined,\n                                children: poster\n                            }) : null\n                        ]\n                    }),\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(RenderWarningIfBlacklist, {})\n                ]\n            }),\n            shouldShowPoster && posterFillMode === \"player-size\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                style: outer,\n                onPointerDown: clickToPlay ? handlePointerDown : undefined,\n                onDoubleClick: doubleClickToFullscreen ? handleDoubleClick : undefined,\n                children: poster\n            }) : null,\n            controls ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Controls, {\n                fps: config.fps,\n                playing: player.playing,\n                toggle: player.toggle,\n                durationInFrames: config.durationInFrames,\n                containerRef: container,\n                onFullscreenButtonClick,\n                isFullscreen,\n                allowFullscreen,\n                showVolumeControls,\n                onExitFullscreenButtonClick,\n                spaceKeyToPlayOrPause,\n                onSeekEnd,\n                onSeekStart,\n                inFrame,\n                outFrame,\n                initiallyShowControls,\n                canvasSize,\n                renderFullscreenButton,\n                renderPlayPauseButton,\n                alwaysShowControls,\n                showPlaybackRateControl,\n                buffering: showBufferIndicator,\n                hideControlsWhenPointerDoesntMove,\n                onDoubleClick: doubleClickToFullscreen ? handleDoubleClick : undefined,\n                onPointerDown: clickToPlay ? handlePointerDown : undefined,\n                renderMuteButton,\n                renderVolumeSlider\n            }) : null\n        ]\n    });\n    if (noSuspense || IS_NODE && !doesReactVersionSupportSuspense) {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n            ref: container,\n            style: outerStyle,\n            className: className2,\n            children: content\n        });\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        ref: container,\n        style: outerStyle,\n        className: className2,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n            fallback: loadingMarkup,\n            children: content\n        })\n    });\n};\nvar PlayerUI_default = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(PlayerUI);\n// src/SharedPlayerContext.tsx\n\n\n// src/volume-persistance.ts\n\nvar DEFAULT_VOLUME_PERSISTANCE_KEY = \"remotion.volumePreference\";\nvar persistVolume = (volume, logLevel, volumePersistenceKey)=>{\n    if (true) {\n        return;\n    }\n    try {\n        window.localStorage.setItem(volumePersistenceKey ?? DEFAULT_VOLUME_PERSISTANCE_KEY, String(volume));\n    } catch (e) {\n        remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.Log.error(logLevel, \"Could not persist volume\", e);\n    }\n};\nvar getPreferredVolume = (volumePersistenceKey)=>{\n    if (true) {\n        return 1;\n    }\n    try {\n        const val = window.localStorage.getItem(volumePersistenceKey ?? DEFAULT_VOLUME_PERSISTANCE_KEY);\n        return val ? Number(val) : 1;\n    } catch  {\n        return 1;\n    }\n};\n// src/SharedPlayerContext.tsx\n\nvar PLAYER_COMP_ID = \"player-comp\";\nvar SharedPlayerContexts = ({ children, timelineContext, fps, compositionHeight, compositionWidth, durationInFrames, component, numberOfSharedAudioTags, initiallyMuted, logLevel, audioLatencyHint, volumePersistenceKey })=>{\n    const compositionManagerContext = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        const context = {\n            compositions: [\n                {\n                    component,\n                    durationInFrames,\n                    height: compositionHeight,\n                    width: compositionWidth,\n                    fps,\n                    id: PLAYER_COMP_ID,\n                    nonce: 777,\n                    folderName: null,\n                    parentFolderName: null,\n                    schema: null,\n                    calculateMetadata: null\n                }\n            ],\n            folders: [],\n            currentCompositionMetadata: null,\n            canvasContent: {\n                type: \"composition\",\n                compositionId: \"player-comp\"\n            }\n        };\n        return context;\n    }, [\n        component,\n        durationInFrames,\n        compositionHeight,\n        compositionWidth,\n        fps\n    ]);\n    const [mediaMuted, setMediaMuted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(()=>initiallyMuted);\n    const [mediaVolume, setMediaVolume] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(()=>getPreferredVolume(volumePersistenceKey ?? null));\n    const mediaVolumeContextValue = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            mediaMuted,\n            mediaVolume\n        };\n    }, [\n        mediaMuted,\n        mediaVolume\n    ]);\n    const setMediaVolumeAndPersist = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((vol)=>{\n        setMediaVolume(vol);\n        persistVolume(vol, logLevel, volumePersistenceKey ?? null);\n    }, [\n        logLevel,\n        volumePersistenceKey\n    ]);\n    const setMediaVolumeContextValue = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            setMediaMuted,\n            setMediaVolume: setMediaVolumeAndPersist\n        };\n    }, [\n        setMediaVolumeAndPersist\n    ]);\n    const logLevelContext = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            logLevel,\n            mountTime: Date.now()\n        };\n    }, [\n        logLevel\n    ]);\n    const env = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            isPlayer: true,\n            isRendering: false,\n            isStudio: false,\n            isClientSideRendering: false,\n            isReadOnlyStudio: false\n        };\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.RemotionEnvironmentContext.Provider, {\n        value: env,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.LogLevelContext.Provider, {\n            value: logLevelContext,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.CanUseRemotionHooksProvider, {\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.Timeline.TimelineContext.Provider, {\n                    value: timelineContext,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.CompositionManager.Provider, {\n                        value: compositionManagerContext,\n                        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.ResolveCompositionConfig, {\n                            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.PrefetchProvider, {\n                                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.DurationsContextProvider, {\n                                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.MediaVolumeContext.Provider, {\n                                        value: mediaVolumeContextValue,\n                                        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.SetMediaVolumeContext.Provider, {\n                                            value: setMediaVolumeContextValue,\n                                            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.SharedAudioContextProvider, {\n                                                numberOfAudioTags: numberOfSharedAudioTags,\n                                                component,\n                                                audioLatencyHint,\n                                                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.BufferingProvider, {\n                                                    children\n                                                })\n                                            })\n                                        })\n                                    })\n                                })\n                            })\n                        })\n                    })\n                })\n            })\n        })\n    });\n};\n// src/use-remotion-license-acknowledge.ts\n\nvar warningShown = false;\nvar acknowledgeRemotionLicenseMessage = (acknowledge, logLevel)=>{\n    if (acknowledge) {\n        return;\n    }\n    if (warningShown) {\n        return;\n    }\n    warningShown = true;\n    remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.Log.warn(logLevel, \"Note: Some companies are required to obtain a license to use Remotion. See: https://remotion.dev/license\\nPass the `acknowledgeRemotionLicense` prop to `<Player />` function to make this message disappear.\");\n};\n// src/utils/validate-in-out-frame.ts\nvar validateSingleFrame = (frame, variableName)=>{\n    if (typeof frame === \"undefined\" || frame === null) {\n        return frame ?? null;\n    }\n    if (typeof frame !== \"number\") {\n        throw new TypeError(`\"${variableName}\" must be a number, but is ${JSON.stringify(frame)}`);\n    }\n    if (Number.isNaN(frame)) {\n        throw new TypeError(`\"${variableName}\" must not be NaN, but is ${JSON.stringify(frame)}`);\n    }\n    if (!Number.isFinite(frame)) {\n        throw new TypeError(`\"${variableName}\" must be finite, but is ${JSON.stringify(frame)}`);\n    }\n    if (frame % 1 !== 0) {\n        throw new TypeError(`\"${variableName}\" must be an integer, but is ${JSON.stringify(frame)}`);\n    }\n    return frame;\n};\nvar validateInOutFrames = ({ inFrame, durationInFrames, outFrame })=>{\n    const validatedInFrame = validateSingleFrame(inFrame, \"inFrame\");\n    const validatedOutFrame = validateSingleFrame(outFrame, \"outFrame\");\n    if (validatedInFrame === null && validatedOutFrame === null) {\n        return;\n    }\n    if (validatedInFrame !== null && validatedInFrame > durationInFrames - 1) {\n        throw new Error(\"inFrame must be less than (durationInFrames - 1), but is \" + validatedInFrame);\n    }\n    if (validatedOutFrame !== null && validatedOutFrame > durationInFrames - 1) {\n        throw new Error(\"outFrame must be less than (durationInFrames - 1), but is \" + validatedOutFrame);\n    }\n    if (validatedInFrame !== null && validatedInFrame < 0) {\n        throw new Error(\"inFrame must be greater than 0, but is \" + validatedInFrame);\n    }\n    if (validatedOutFrame !== null && validatedOutFrame <= 0) {\n        throw new Error(`outFrame must be greater than 0, but is ${validatedOutFrame}. If you want to render a single frame, use <Thumbnail /> instead.`);\n    }\n    if (validatedOutFrame !== null && validatedInFrame !== null && validatedOutFrame <= validatedInFrame) {\n        throw new Error(\"outFrame must be greater than inFrame, but is \" + validatedOutFrame + \" <= \" + validatedInFrame);\n    }\n};\n// src/utils/validate-initial-frame.ts\nvar validateInitialFrame = ({ initialFrame, durationInFrames })=>{\n    if (typeof durationInFrames !== \"number\") {\n        throw new Error(`\\`durationInFrames\\` must be a number, but is ${JSON.stringify(durationInFrames)}`);\n    }\n    if (typeof initialFrame === \"undefined\") {\n        return;\n    }\n    if (typeof initialFrame !== \"number\") {\n        throw new Error(`\\`initialFrame\\` must be a number, but is ${JSON.stringify(initialFrame)}`);\n    }\n    if (Number.isNaN(initialFrame)) {\n        throw new Error(`\\`initialFrame\\` must be a number, but is NaN`);\n    }\n    if (!Number.isFinite(initialFrame)) {\n        throw new Error(`\\`initialFrame\\` must be a number, but is Infinity`);\n    }\n    if (initialFrame % 1 !== 0) {\n        throw new Error(`\\`initialFrame\\` must be an integer, but is ${JSON.stringify(initialFrame)}`);\n    }\n    if (initialFrame > durationInFrames - 1) {\n        throw new Error(`\\`initialFrame\\` must be less or equal than \\`durationInFrames - 1\\`, but is ${JSON.stringify(initialFrame)}`);\n    }\n};\n// src/utils/validate-playbackrate.ts\nvar validatePlaybackRate = (playbackRate)=>{\n    if (playbackRate === undefined) {\n        return;\n    }\n    if (playbackRate > 4) {\n        throw new Error(`The highest possible playback rate is 4. You passed: ${playbackRate}`);\n    }\n    if (playbackRate < -4) {\n        throw new Error(`The lowest possible playback rate is -4. You passed: ${playbackRate}`);\n    }\n    if (playbackRate === 0) {\n        throw new Error(`A playback rate of 0 is not supported.`);\n    }\n};\n// src/validate.ts\n\nvar validateFps = remotion_no_react__WEBPACK_IMPORTED_MODULE_3__.NoReactInternals.validateFps;\nvar validateDimension = remotion_no_react__WEBPACK_IMPORTED_MODULE_3__.NoReactInternals.validateDimension;\nvar validateDurationInFrames = remotion_no_react__WEBPACK_IMPORTED_MODULE_3__.NoReactInternals.validateDurationInFrames;\nvar validateDefaultAndInputProps = remotion_no_react__WEBPACK_IMPORTED_MODULE_3__.NoReactInternals.validateDefaultAndInputProps;\n// src/Player.tsx\n\nvar componentOrNullIfLazy = (props)=>{\n    if (\"component\" in props) {\n        return props.component;\n    }\n    return null;\n};\nvar PlayerFn = ({ durationInFrames, compositionHeight, compositionWidth, fps, inputProps, style: style2, controls = false, loop = false, autoPlay = false, showVolumeControls = true, allowFullscreen = true, clickToPlay, doubleClickToFullscreen = false, spaceKeyToPlayOrPause = true, moveToBeginningWhenEnded = true, numberOfSharedAudioTags = 5, errorFallback = ()=>\"⚠️\", playbackRate = 1, renderLoading, className: className2, showPosterWhenUnplayed, showPosterWhenEnded, showPosterWhenPaused, showPosterWhenBuffering, showPosterWhenBufferingAndPaused, initialFrame, renderPoster, inFrame, outFrame, initiallyShowControls, renderFullscreenButton, renderPlayPauseButton, renderVolumeSlider, alwaysShowControls = false, initiallyMuted = false, showPlaybackRateControl = false, posterFillMode = \"player-size\", bufferStateDelayInMilliseconds, hideControlsWhenPointerDoesntMove = true, overflowVisible = false, renderMuteButton, browserMediaControlsBehavior: passedBrowserMediaControlsBehavior, overrideInternalClassName, logLevel = \"info\", noSuspense, acknowledgeRemotionLicense, audioLatencyHint = \"interactive\", volumePersistenceKey, ...componentProps }, ref)=>{\n    if (false) {}\n    if (componentProps.defaultProps !== undefined) {\n        throw new Error(\"The <Player /> component does not accept `defaultProps`, but some were passed. Use `inputProps` instead.\");\n    }\n    const componentForValidation = componentOrNullIfLazy(componentProps);\n    if (componentForValidation?.type === remotion__WEBPACK_IMPORTED_MODULE_1__.Composition) {\n        throw new TypeError(`'component' should not be an instance of <Composition/>. Pass the React component directly, and set the duration, fps and dimensions as separate props. See https://www.remotion.dev/docs/player/examples for an example.`);\n    }\n    if (componentForValidation === remotion__WEBPACK_IMPORTED_MODULE_1__.Composition) {\n        throw new TypeError(`'component' must not be the 'Composition' component. Pass your own React component directly, and set the duration, fps and dimensions as separate props. See https://www.remotion.dev/docs/player/examples for an example.`);\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(()=>acknowledgeRemotionLicenseMessage(Boolean(acknowledgeRemotionLicense), logLevel));\n    const component = remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.useLazyComponent({\n        compProps: componentProps,\n        componentName: \"Player\",\n        noSuspense: Boolean(noSuspense)\n    });\n    validateInitialFrame({\n        initialFrame,\n        durationInFrames\n    });\n    const [frame, setFrame] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(()=>({\n            [PLAYER_COMP_ID]: initialFrame ?? 0\n        }));\n    const [playing, setPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [rootId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"player-comp\");\n    const rootRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const audioAndVideoTags = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)([]);\n    const imperativePlaying = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(false);\n    const [currentPlaybackRate, setCurrentPlaybackRate] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(playbackRate);\n    if (typeof compositionHeight !== \"number\") {\n        throw new TypeError(`'compositionHeight' must be a number but got '${typeof compositionHeight}' instead`);\n    }\n    if (typeof compositionWidth !== \"number\") {\n        throw new TypeError(`'compositionWidth' must be a number but got '${typeof compositionWidth}' instead`);\n    }\n    validateDimension(compositionHeight, \"compositionHeight\", \"of the <Player /> component\");\n    validateDimension(compositionWidth, \"compositionWidth\", \"of the <Player /> component\");\n    validateDurationInFrames(durationInFrames, {\n        component: \"of the <Player/> component\",\n        allowFloats: false\n    });\n    validateFps(fps, \"as a prop of the <Player/> component\", false);\n    validateDefaultAndInputProps(inputProps, \"inputProps\", null);\n    validateInOutFrames({\n        durationInFrames,\n        inFrame,\n        outFrame\n    });\n    if (typeof controls !== \"boolean\" && typeof controls !== \"undefined\") {\n        throw new TypeError(`'controls' must be a boolean or undefined but got '${typeof controls}' instead`);\n    }\n    if (typeof autoPlay !== \"boolean\" && typeof autoPlay !== \"undefined\") {\n        throw new TypeError(`'autoPlay' must be a boolean or undefined but got '${typeof autoPlay}' instead`);\n    }\n    if (typeof loop !== \"boolean\" && typeof loop !== \"undefined\") {\n        throw new TypeError(`'loop' must be a boolean or undefined but got '${typeof loop}' instead`);\n    }\n    if (typeof doubleClickToFullscreen !== \"boolean\" && typeof doubleClickToFullscreen !== \"undefined\") {\n        throw new TypeError(`'doubleClickToFullscreen' must be a boolean or undefined but got '${typeof doubleClickToFullscreen}' instead`);\n    }\n    if (typeof showVolumeControls !== \"boolean\" && typeof showVolumeControls !== \"undefined\") {\n        throw new TypeError(`'showVolumeControls' must be a boolean or undefined but got '${typeof showVolumeControls}' instead`);\n    }\n    if (typeof allowFullscreen !== \"boolean\" && typeof allowFullscreen !== \"undefined\") {\n        throw new TypeError(`'allowFullscreen' must be a boolean or undefined but got '${typeof allowFullscreen}' instead`);\n    }\n    if (typeof clickToPlay !== \"boolean\" && typeof clickToPlay !== \"undefined\") {\n        throw new TypeError(`'clickToPlay' must be a boolean or undefined but got '${typeof clickToPlay}' instead`);\n    }\n    if (typeof spaceKeyToPlayOrPause !== \"boolean\" && typeof spaceKeyToPlayOrPause !== \"undefined\") {\n        throw new TypeError(`'spaceKeyToPlayOrPause' must be a boolean or undefined but got '${typeof spaceKeyToPlayOrPause}' instead`);\n    }\n    if (typeof numberOfSharedAudioTags !== \"number\" || numberOfSharedAudioTags % 1 !== 0 || !Number.isFinite(numberOfSharedAudioTags) || Number.isNaN(numberOfSharedAudioTags) || numberOfSharedAudioTags < 0) {\n        throw new TypeError(`'numberOfSharedAudioTags' must be an integer but got '${numberOfSharedAudioTags}' instead`);\n    }\n    validatePlaybackRate(currentPlaybackRate);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        setCurrentPlaybackRate(playbackRate);\n    }, [\n        playbackRate\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, ()=>rootRef.current, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(()=>{\n        remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.playbackLogging({\n            logLevel,\n            message: `[player] Mounting <Player>. User agent = ${typeof navigator === \"undefined\" ? \"server\" : navigator.userAgent}`,\n            tag: \"player\",\n            mountTime: Date.now()\n        });\n    });\n    const timelineContextValue = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            frame,\n            playing,\n            rootId,\n            playbackRate: currentPlaybackRate,\n            imperativePlaying,\n            setPlaybackRate: (rate)=>{\n                setCurrentPlaybackRate(rate);\n            },\n            audioAndVideoTags\n        };\n    }, [\n        frame,\n        currentPlaybackRate,\n        playing,\n        rootId\n    ]);\n    const setTimelineContextValue = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            setFrame,\n            setPlaying\n        };\n    }, [\n        setFrame\n    ]);\n    if (false) {}\n    const actualInputProps = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>inputProps ?? {}, [\n        inputProps\n    ]);\n    const browserMediaControlsBehavior = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return passedBrowserMediaControlsBehavior ?? {\n            mode: \"prevent-media-session\"\n        };\n    }, [\n        passedBrowserMediaControlsBehavior\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.IsPlayerContextProvider, {\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SharedPlayerContexts, {\n            timelineContext: timelineContextValue,\n            component,\n            compositionHeight,\n            compositionWidth,\n            durationInFrames,\n            fps,\n            numberOfSharedAudioTags,\n            initiallyMuted,\n            logLevel,\n            audioLatencyHint,\n            volumePersistenceKey,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.Timeline.SetTimelineContext.Provider, {\n                value: setTimelineContextValue,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PlayerEmitterProvider, {\n                    currentPlaybackRate,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PlayerUI_default, {\n                        ref: rootRef,\n                        posterFillMode,\n                        renderLoading,\n                        autoPlay: Boolean(autoPlay),\n                        loop: Boolean(loop),\n                        controls: Boolean(controls),\n                        errorFallback,\n                        style: style2,\n                        inputProps: actualInputProps,\n                        allowFullscreen: Boolean(allowFullscreen),\n                        moveToBeginningWhenEnded: Boolean(moveToBeginningWhenEnded),\n                        clickToPlay: typeof clickToPlay === \"boolean\" ? clickToPlay : Boolean(controls),\n                        showVolumeControls: Boolean(showVolumeControls),\n                        doubleClickToFullscreen: Boolean(doubleClickToFullscreen),\n                        spaceKeyToPlayOrPause: Boolean(spaceKeyToPlayOrPause),\n                        playbackRate: currentPlaybackRate,\n                        className: className2 ?? undefined,\n                        showPosterWhenUnplayed: Boolean(showPosterWhenUnplayed),\n                        showPosterWhenEnded: Boolean(showPosterWhenEnded),\n                        showPosterWhenPaused: Boolean(showPosterWhenPaused),\n                        showPosterWhenBuffering: Boolean(showPosterWhenBuffering),\n                        showPosterWhenBufferingAndPaused: Boolean(showPosterWhenBufferingAndPaused),\n                        renderPoster,\n                        inFrame: inFrame ?? null,\n                        outFrame: outFrame ?? null,\n                        initiallyShowControls: initiallyShowControls ?? true,\n                        renderFullscreen: renderFullscreenButton ?? null,\n                        renderPlayPauseButton: renderPlayPauseButton ?? null,\n                        renderMuteButton: renderMuteButton ?? null,\n                        renderVolumeSlider: renderVolumeSlider ?? null,\n                        alwaysShowControls,\n                        showPlaybackRateControl,\n                        bufferStateDelayInMilliseconds: bufferStateDelayInMilliseconds ?? 300,\n                        hideControlsWhenPointerDoesntMove,\n                        overflowVisible,\n                        browserMediaControlsBehavior,\n                        overrideInternalClassName: overrideInternalClassName ?? undefined,\n                        noSuspense: Boolean(noSuspense)\n                    })\n                })\n            })\n        })\n    });\n};\nvar forward = react__WEBPACK_IMPORTED_MODULE_2__.forwardRef;\nvar Player = forward(PlayerFn);\n// src/Thumbnail.tsx\n\n\n// src/ThumbnailUI.tsx\n\n\n// src/use-thumbnail.ts\n\nvar useThumbnail = ()=>{\n    const emitter = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(ThumbnailEmitterContext);\n    if (!emitter) {\n        throw new TypeError(\"Expected Player event emitter context\");\n    }\n    const returnValue = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            emitter\n        };\n    }, [\n        emitter\n    ]);\n    return returnValue;\n};\n// src/ThumbnailUI.tsx\n\nvar reactVersion2 = react__WEBPACK_IMPORTED_MODULE_2__.version.split(\".\")[0];\nif (reactVersion2 === \"0\") {\n    throw new Error(`Version ${reactVersion2} of \"react\" is not supported by Remotion`);\n}\nvar doesReactVersionSupportSuspense2 = parseInt(reactVersion2, 10) >= 18;\nvar ThumbnailUI = ({ style: style2, inputProps, errorFallback, renderLoading, className: className2, overflowVisible, noSuspense, overrideInternalClassName }, ref)=>{\n    const config = remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.useUnsafeVideoConfig();\n    const video = remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.useVideo();\n    const container = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const canvasSize = useElementSize(container, {\n        triggerOnWindowResize: false,\n        shouldApplyCssTransforms: false\n    });\n    const layout = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        if (!config || !canvasSize) {\n            return null;\n        }\n        return calculateCanvasTransformation({\n            canvasSize,\n            compositionHeight: config.height,\n            compositionWidth: config.width,\n            previewSize: \"auto\"\n        });\n    }, [\n        canvasSize,\n        config\n    ]);\n    const scale = layout?.scale ?? 1;\n    const thumbnail = useThumbnail();\n    useBufferStateEmitter(thumbnail.emitter);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, ()=>{\n        const methods = {\n            getContainerNode: ()=>container.current,\n            getScale: ()=>scale\n        };\n        return Object.assign(thumbnail.emitter, methods);\n    }, [\n        scale,\n        thumbnail.emitter\n    ]);\n    const VideoComponent = video ? video.component : null;\n    const outerStyle = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return calculateOuterStyle({\n            config,\n            style: style2,\n            canvasSize,\n            overflowVisible,\n            layout\n        });\n    }, [\n        canvasSize,\n        config,\n        layout,\n        overflowVisible,\n        style2\n    ]);\n    const outer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return calculateOuter({\n            config,\n            layout,\n            scale,\n            overflowVisible\n        });\n    }, [\n        config,\n        layout,\n        overflowVisible,\n        scale\n    ]);\n    const containerStyle3 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return calculateContainerStyle({\n            config,\n            layout,\n            scale,\n            overflowVisible\n        });\n    }, [\n        config,\n        layout,\n        overflowVisible,\n        scale\n    ]);\n    const onError = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)((error)=>{\n        thumbnail.emitter.dispatchError(error);\n    }, [\n        thumbnail.emitter\n    ]);\n    const loadingMarkup = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return renderLoading ? renderLoading({\n            height: outerStyle.height,\n            width: outerStyle.width,\n            isBuffering: false\n        }) : null;\n    }, [\n        outerStyle.height,\n        outerStyle.width,\n        renderLoading\n    ]);\n    const currentScaleContext = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return {\n            type: \"scale\",\n            scale\n        };\n    }, [\n        scale\n    ]);\n    if (!config) {\n        return null;\n    }\n    const content = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        style: outer,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n            style: containerStyle3,\n            className: playerCssClassname(overrideInternalClassName),\n            children: VideoComponent ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ErrorBoundary, {\n                onError,\n                errorFallback,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.CurrentScaleContext.Provider, {\n                    value: currentScaleContext,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(VideoComponent, {\n                        ...video?.props ?? {},\n                        ...inputProps ?? {}\n                    })\n                })\n            }) : null\n        })\n    });\n    if (noSuspense || IS_NODE && !doesReactVersionSupportSuspense2) {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n            ref: container,\n            style: outerStyle,\n            className: className2,\n            children: content\n        });\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        ref: container,\n        style: outerStyle,\n        className: className2,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react__WEBPACK_IMPORTED_MODULE_2__.Suspense, {\n            fallback: loadingMarkup,\n            children: content\n        })\n    });\n};\nvar ThumbnailUI_default = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.forwardRef)(ThumbnailUI);\n// src/Thumbnail.tsx\n\nvar ThumbnailFn = ({ frameToDisplay, style: style2, inputProps, compositionHeight, compositionWidth, durationInFrames, fps, className: className2, errorFallback = ()=>\"⚠️\", renderLoading, overflowVisible = false, overrideInternalClassName, logLevel = \"info\", noSuspense, ...componentProps }, ref)=>{\n    if (false) {}\n    const [thumbnailId] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(()=>String((0,remotion__WEBPACK_IMPORTED_MODULE_1__.random)(null)));\n    const rootRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const timelineState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        const value = {\n            playing: false,\n            frame: {\n                [PLAYER_COMP_ID]: frameToDisplay\n            },\n            rootId: thumbnailId,\n            imperativePlaying: {\n                current: false\n            },\n            playbackRate: 1,\n            setPlaybackRate: ()=>{\n                throw new Error(\"thumbnail\");\n            },\n            audioAndVideoTags: {\n                current: []\n            }\n        };\n        return value;\n    }, [\n        frameToDisplay,\n        thumbnailId\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useImperativeHandle)(ref, ()=>rootRef.current, []);\n    const Component = remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.useLazyComponent({\n        compProps: componentProps,\n        componentName: \"Thumbnail\",\n        noSuspense: Boolean(noSuspense)\n    });\n    const [emitter] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(()=>new ThumbnailEmitter);\n    const passedInputProps = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        return inputProps ?? {};\n    }, [\n        inputProps\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(remotion__WEBPACK_IMPORTED_MODULE_1__.Internals.IsPlayerContextProvider, {\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SharedPlayerContexts, {\n            timelineContext: timelineState,\n            component: Component,\n            compositionHeight,\n            compositionWidth,\n            durationInFrames,\n            fps,\n            numberOfSharedAudioTags: 0,\n            initiallyMuted: true,\n            logLevel,\n            audioLatencyHint: \"playback\",\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ThumbnailEmitterContext.Provider, {\n                value: emitter,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ThumbnailUI_default, {\n                    ref: rootRef,\n                    className: className2,\n                    errorFallback,\n                    inputProps: passedInputProps,\n                    renderLoading,\n                    style: style2,\n                    overflowVisible,\n                    overrideInternalClassName,\n                    noSuspense: Boolean(noSuspense)\n                })\n            })\n        })\n    });\n};\nvar forward2 = react__WEBPACK_IMPORTED_MODULE_2__.forwardRef;\nvar Thumbnail = forward2(ThumbnailFn);\n// src/index.ts\nvar PlayerInternals = {\n    PlayerEventEmitterContext,\n    PlayerEmitter,\n    usePlayer,\n    usePlayback,\n    useElementSize,\n    calculateCanvasTransformation,\n    useHoverState,\n    updateAllElementsSizes,\n    PlayerEmitterProvider,\n    BufferingIndicator,\n    useFrameImperative\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@remotion/player/dist/esm/index.mjs\n");

/***/ })

};
;