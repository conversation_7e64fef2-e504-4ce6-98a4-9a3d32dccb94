/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CPiyushWorkspace%5Ccode%5COnest%5Cvideo%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CPiyushWorkspace%5Ccode%5COnest%5Cvideo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CPiyushWorkspace%5Ccode%5COnest%5Cvideo%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CPiyushWorkspace%5Ccode%5COnest%5Cvideo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9100\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CPiyushWorkspace%5Ccode%5COnest%5Cvideo%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CPiyushWorkspace%5Ccode%5COnest%5Cvideo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQaXl1c2hXb3Jrc3BhY2UlNUMlNUNjb2RlJTVDJTVDT25lc3QlNUMlNUN2aWRlbyU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnSkFBZ0ciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9yZW1vdGlvbi1nZW1pbmktdmlkZW8tZ2VuZXJhdG9yLz9hNmRlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcUGl5dXNoV29ya3NwYWNlXFxcXGNvZGVcXFxcT25lc3RcXFxcdmlkZW9cXFxcc3JjXFxcXGFwcFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CPiyushWorkspace%5C%5Ccode%5C%5COnest%5C%5Cvideo%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _remotion_player__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @remotion/player */ \"(ssr)/./node_modules/@remotion/player/dist/esm/index.mjs\");\n/* harmony import */ var _remotion_CodeVideo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/remotion/CodeVideo */ \"(ssr)/./src/remotion/CodeVideo.tsx\");\n/* harmony import */ var _hooks_useChat__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useChat */ \"(ssr)/./src/hooks/useChat.ts\");\n/* harmony import */ var _components_ChatInterface__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ChatInterface */ \"(ssr)/./src/components/ChatInterface.tsx\");\n/* harmony import */ var _components_VideoControls__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/VideoControls */ \"(ssr)/./src/components/VideoControls.tsx\");\n/* harmony import */ var _components_CodeToVideo__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/CodeToVideo */ \"(ssr)/./src/components/CodeToVideo.tsx\");\n/* harmony import */ var _barrel_optimize_names_Code2_MessageSquare_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Code2,MessageSquare,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_Code2_MessageSquare_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Code2,MessageSquare,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code-xml.js\");\n/* harmony import */ var _barrel_optimize_names_Code2_MessageSquare_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Code2,MessageSquare,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nconst defaultVideoData = {\n    code: `function fibonacci(n) {\n  if (n <= 1) return n;\n  return fibonacci(n - 1) + fibonacci(n - 2);\n}\n\nconsole.log(fibonacci(10)); // Output: 55`,\n    language: \"javascript\",\n    title: \"Fibonacci Function\",\n    theme: \"dark\"\n};\nfunction Home() {\n    const [videoData, setVideoData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaultVideoData);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentFrame, setCurrentFrame] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [showSettings, setShowSettings] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"chat\");\n    const [isGeneratingVideo, setIsGeneratingVideo] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { messages, isLoading, error, sendMessage, generateCodeFromPrompt, clearMessages, clearError } = (0,_hooks_useChat__WEBPACK_IMPORTED_MODULE_3__.useChat)();\n    const videoConfig = {\n        fps: 30,\n        durationInFrames: 300,\n        width: 1920,\n        height: 1080\n    };\n    const handleCodeGenerated = (codeResponse)=>{\n        setVideoData({\n            code: codeResponse.code,\n            language: codeResponse.language,\n            title: codeResponse.title || \"Generated Code\",\n            theme: \"dark\"\n        });\n    };\n    const handleDirectCodeToVideo = (codeResponse)=>{\n        setIsGeneratingVideo(true);\n        // Simulate processing time for better UX\n        setTimeout(()=>{\n            setVideoData({\n                code: codeResponse.code,\n                language: codeResponse.language,\n                title: codeResponse.title || \"Generated Code\",\n                theme: \"dark\"\n            });\n            setIsGeneratingVideo(false);\n            // Auto-play the video\n            setIsPlaying(true);\n            setCurrentFrame(0);\n        }, 1000);\n    };\n    const handlePlayPause = ()=>{\n        setIsPlaying(!isPlaying);\n    };\n    const handleRestart = ()=>{\n        setCurrentFrame(0);\n        setIsPlaying(true);\n    };\n    const handleThemeToggle = ()=>{\n        setVideoData((prev)=>({\n                ...prev,\n                theme: prev.theme === \"dark\" ? \"light\" : \"dark\"\n            }));\n    };\n    const handleLanguageChange = (language)=>{\n        setVideoData((prev)=>({\n                ...prev,\n                language\n            }));\n    };\n    const handleExportVideo = ()=>{\n        // This would typically trigger a server-side render\n        alert(\"Video export functionality would be implemented with a backend service\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-center bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent\",\n                            children: \"Remotion + Gemini Code Video Generator\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-center text-gray-400 mt-2\",\n                            children: \"Generate animated code videos using Gemini 2.0 Flash and Remotion\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 101,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 rounded-xl shadow-2xl border border-gray-700 overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex border-b border-gray-700\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"chat\"),\n                                                className: `flex-1 px-6 py-4 text-sm font-medium transition-colors ${activeTab === \"chat\" ? \"bg-blue-600 text-white\" : \"bg-gray-800 text-gray-300 hover:bg-gray-700\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code2_MessageSquare_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-4 h-4 inline mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 127,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Chat with AI\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"code\"),\n                                                className: `flex-1 px-6 py-4 text-sm font-medium transition-colors ${activeTab === \"code\" ? \"bg-blue-600 text-white\" : \"bg-gray-800 text-gray-300 hover:bg-gray-700\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code2_MessageSquare_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"w-4 h-4 inline mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Code to Video\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 130,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6\",\n                                        children: activeTab === \"chat\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"text-2xl font-semibold mb-4 flex items-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"w-3 h-3 bg-green-500 rounded-full mr-3 animate-pulse\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 147,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Chat with Gemini\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChatInterface__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    messages: messages,\n                                                    isLoading: isLoading,\n                                                    error: error,\n                                                    onSendMessage: sendMessage,\n                                                    onCodeGenerated: handleCodeGenerated,\n                                                    onClearMessages: clearMessages,\n                                                    onClearError: clearError\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CodeToVideo__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            onGenerateVideo: handleDirectCodeToVideo,\n                                            isGenerating: isGeneratingVideo\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 11\n                            }, this),\n                            activeTab === \"chat\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 rounded-xl p-6 shadow-2xl border border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Quick Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>generateCodeFromPrompt(\"Create a React component for a todo list\"),\n                                                className: \"btn-primary text-sm\",\n                                                disabled: isLoading,\n                                                children: \"React Todo\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>generateCodeFromPrompt(\"Write a Python function to sort an array\"),\n                                                className: \"btn-primary text-sm\",\n                                                disabled: isLoading,\n                                                children: \"Python Sort\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>generateCodeFromPrompt(\"Create a JavaScript API client\"),\n                                                className: \"btn-primary text-sm\",\n                                                disabled: isLoading,\n                                                children: \"JS API Client\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>generateCodeFromPrompt(\"Write a CSS animation for a loading spinner\"),\n                                                className: \"btn-primary text-sm\",\n                                                disabled: isLoading,\n                                                children: \"CSS Animation\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 rounded-xl p-6 shadow-2xl border border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-semibold flex items-center\",\n                                                children: [\n                                                    \"Video Preview\",\n                                                    isGeneratingVideo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-3 flex items-center text-sm text-blue-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"spinner mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Generating...\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 211,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setShowSettings(!showSettings),\n                                                className: \"p-2 hover:bg-gray-700 rounded-lg transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code2_MessageSquare_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 220,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"video-container mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_remotion_player__WEBPACK_IMPORTED_MODULE_10__.Player, {\n                                            component: _remotion_CodeVideo__WEBPACK_IMPORTED_MODULE_2__.CodeVideo,\n                                            inputProps: videoData,\n                                            durationInFrames: videoConfig.durationInFrames,\n                                            fps: videoConfig.fps,\n                                            compositionWidth: videoConfig.width,\n                                            compositionHeight: videoConfig.height,\n                                            style: {\n                                                width: \"100%\",\n                                                aspectRatio: \"16/9\"\n                                            },\n                                            controls: false,\n                                            loop: true,\n                                            autoPlay: isPlaying,\n                                            acknowledgeRemotionLicense: true\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 230,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_VideoControls__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        isPlaying: isPlaying,\n                                        onPlayPause: handlePlayPause,\n                                        onRestart: handleRestart,\n                                        onExport: handleExportVideo,\n                                        currentFrame: currentFrame,\n                                        totalFrames: videoConfig.durationInFrames,\n                                        fps: videoConfig.fps\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, this),\n                                    showSettings && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 p-4 bg-gray-700 rounded-lg border border-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-semibold mb-3\",\n                                                children: \"Video Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-1\",\n                                                                children: \"Theme\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleThemeToggle,\n                                                                className: `px-3 py-1 rounded text-sm ${videoData.theme === \"dark\" ? \"bg-gray-900 text-white\" : \"bg-gray-100 text-gray-900\"}`,\n                                                                children: videoData.theme === \"dark\" ? \"Dark\" : \"Light\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-1\",\n                                                                children: \"Language\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 278,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                value: videoData.language,\n                                                                onChange: (e)=>handleLanguageChange(e.target.value),\n                                                                className: \"bg-gray-600 border border-gray-500 rounded px-3 py-1 text-sm\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"javascript\",\n                                                                        children: \"JavaScript\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 284,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"python\",\n                                                                        children: \"Python\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 285,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"typescript\",\n                                                                        children: \"TypeScript\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"java\",\n                                                                        children: \"Java\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 287,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"cpp\",\n                                                                        children: \"C++\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 288,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"rust\",\n                                                                        children: \"Rust\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 289,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"go\",\n                                                                        children: \"Go\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 290,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 279,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"block text-sm font-medium mb-1\",\n                                                                children: \"Title\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: videoData.title,\n                                                                onChange: (e)=>setVideoData((prev)=>({\n                                                                            ...prev,\n                                                                            title: e.target.value\n                                                                        })),\n                                                                className: \"bg-gray-600 border border-gray-500 rounded px-3 py-1 text-sm w-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 263,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-800 rounded-xl p-6 shadow-2xl border border-gray-700\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-4\",\n                                        children: \"Current Code\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-900 rounded-lg p-4 font-mono text-sm overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                            className: \"text-gray-300 whitespace-pre-wrap\",\n                                            children: videoData.code\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 311,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 310,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-3 flex items-center justify-between text-sm text-gray-400\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Language: \",\n                                                    videoData.language\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"Lines: \",\n                                                    videoData.code.split(\"\\n\").length\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"mt-16 text-center text-gray-500\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Built with Next.js, Remotion, and Gemini 2.0 Flash\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 322,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ChatInterface.tsx":
/*!******************************************!*\
  !*** ./src/components/ChatInterface.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Code_Copy_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Code,Copy,Send,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Code_Copy_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Code,Copy,Send,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Code_Copy_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Code,Copy,Send,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Code_Copy_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Code,Copy,Send,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Code_Copy_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Code,Copy,Send,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Check_Code_Copy_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Check,Code,Copy,Send,Trash2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst ChatInterface = ({ messages, isLoading, error, onSendMessage, onCodeGenerated, onClearMessages, onClearError })=>{\n    const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [copiedMessageId, setCopiedMessageId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const scrollToBottom = ()=>{\n        messagesEndRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        scrollToBottom();\n    }, [\n        messages\n    ]);\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!input.trim() || isLoading) return;\n        const message = input.trim();\n        setInput(\"\");\n        await onSendMessage(message);\n    };\n    const handleKeyDown = (e)=>{\n        if (e.key === \"Enter\" && !e.shiftKey) {\n            e.preventDefault();\n            handleSubmit(e);\n        }\n    };\n    const handleUseCode = (codeResponse)=>{\n        onCodeGenerated(codeResponse);\n    };\n    const handleCopyCode = async (code, messageId)=>{\n        try {\n            await navigator.clipboard.writeText(code);\n            setCopiedMessageId(messageId);\n            setTimeout(()=>setCopiedMessageId(null), 2000);\n        } catch (err) {\n            console.error(\"Failed to copy code:\", err);\n        }\n    };\n    const formatTimestamp = (date)=>{\n        return date.toLocaleTimeString([], {\n            hour: \"2-digit\",\n            minute: \"2-digit\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-96\",\n        children: [\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Code_Copy_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                className: \"w-5 h-5 text-red-400 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-red-200 text-sm\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClearError,\n                        className: \"text-red-400 hover:text-red-300 ml-2\",\n                        children: \"\\xd7\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto custom-scrollbar space-y-4 mb-4\",\n                children: [\n                    messages.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-500 py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Code_Copy_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-12 h-12 mx-auto mb-4 opacity-50\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"Start a conversation with Gemini!\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm mt-2\",\n                                children: \"Try asking to generate code, explain concepts, or get help with programming.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 11\n                    }, undefined) : messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `message-enter ${message.role === \"user\" ? \"ml-8\" : \"mr-8\"}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `p-4 rounded-lg ${message.role === \"user\" ? \"bg-blue-600 text-white ml-auto\" : \"bg-gray-700 text-gray-100\"}`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"whitespace-pre-wrap\",\n                                                    children: message.content\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                message.codeResponse && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-4 bg-gray-800 rounded-lg overflow-hidden border border-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between p-3 bg-gray-900 border-b border-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Code_Copy_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                                            className: \"w-4 h-4 mr-2 text-blue-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 123,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm font-medium text-gray-300\",\n                                                                            children: [\n                                                                                message.codeResponse.title,\n                                                                                \" (\",\n                                                                                message.codeResponse.language,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 124,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 122,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex space-x-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleCopyCode(message.codeResponse.code, message.id),\n                                                                            className: \"p-1 hover:bg-gray-700 rounded text-gray-400 hover:text-gray-200 transition-colors\",\n                                                                            title: \"Copy code\",\n                                                                            children: copiedMessageId === message.id ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Code_Copy_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                                className: \"w-4 h-4 text-green-400\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                                                                lineNumber: 135,\n                                                                                columnNumber: 33\n                                                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Code_Copy_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                className: \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                                                                lineNumber: 137,\n                                                                                columnNumber: 33\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 129,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleUseCode(message.codeResponse),\n                                                                            className: \"px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors\",\n                                                                            children: \"Use in Video\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                                                            lineNumber: 140,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                                                    lineNumber: 128,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                            className: \"p-4 text-sm font-mono text-gray-300 overflow-x-auto\",\n                                                            children: message.codeResponse.code\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                                            lineNumber: 148,\n                                                            columnNumber: 25\n                                                        }, undefined),\n                                                        message.codeResponse.explanation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-3 bg-gray-750 border-t border-gray-600 text-sm text-gray-400\",\n                                                            children: message.codeResponse.explanation\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                                            lineNumber: 152,\n                                                            columnNumber: 27\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs opacity-70 mt-2\",\n                                        children: formatTimestamp(message.timestamp)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 15\n                            }, undefined)\n                        }, message.id, false, {\n                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 13\n                        }, undefined)),\n                    isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-700 text-gray-100 p-4 rounded-lg\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"spinner mr-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Gemini is thinking...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                        lineNumber: 174,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit,\n                className: \"flex space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                ref: inputRef,\n                                value: input,\n                                onChange: (e)=>setInput(e.target.value),\n                                onKeyDown: handleKeyDown,\n                                placeholder: \"Ask Gemini to generate code or help with programming...\",\n                                className: \"w-full p-3 bg-gray-700 border border-gray-600 rounded-lg resize-none chat-input text-gray-100 placeholder-gray-400\",\n                                rows: 2,\n                                disabled: isLoading\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                lineNumber: 186,\n                                columnNumber: 11\n                            }, undefined),\n                            input.trim() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute bottom-2 right-2 text-xs text-gray-500\",\n                                children: \"Press Enter to send, Shift+Enter for new line\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col space-y-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: !input.trim() || isLoading,\n                                className: \"p-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Code_Copy_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, undefined),\n                            messages.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: onClearMessages,\n                                className: \"p-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors\",\n                                title: \"Clear messages\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Check_Code_Copy_Send_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\ChatInterface.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ChatInterface);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ChatInterface.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/CodeToVideo.tsx":
/*!****************************************!*\
  !*** ./src/components/CodeToVideo.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Code2_Copy_Settings_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Code2,Copy,Settings,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/code-xml.js\");\n/* harmony import */ var _barrel_optimize_names_Code2_Copy_Settings_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Code2,Copy,Settings,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Code2_Copy_Settings_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Code2,Copy,Settings,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/wand-sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Code2_Copy_Settings_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Code2,Copy,Settings,Wand2!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _lib_gemini__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/gemini */ \"(ssr)/./src/lib/gemini.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nconst CodeToVideo = ({ onGenerateVideo, isGenerating = false })=>{\n    const [code, setCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"javascript\");\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [theme, setTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"dark\");\n    const [autoDetectLanguage, setAutoDetectLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const supportedLanguages = [\n        {\n            value: \"javascript\",\n            label: \"JavaScript\"\n        },\n        {\n            value: \"typescript\",\n            label: \"TypeScript\"\n        },\n        {\n            value: \"python\",\n            label: \"Python\"\n        },\n        {\n            value: \"java\",\n            label: \"Java\"\n        },\n        {\n            value: \"cpp\",\n            label: \"C++\"\n        },\n        {\n            value: \"rust\",\n            label: \"Rust\"\n        },\n        {\n            value: \"go\",\n            label: \"Go\"\n        },\n        {\n            value: \"html\",\n            label: \"HTML\"\n        },\n        {\n            value: \"css\",\n            label: \"CSS\"\n        },\n        {\n            value: \"sql\",\n            label: \"SQL\"\n        },\n        {\n            value: \"bash\",\n            label: \"Bash\"\n        },\n        {\n            value: \"json\",\n            label: \"JSON\"\n        }\n    ];\n    const exampleCodes = {\n        javascript: `function fibonacci(n) {\n  if (n <= 1) return n;\n  return fibonacci(n - 1) + fibonacci(n - 2);\n}\n\nconsole.log(fibonacci(10)); // Output: 55`,\n        python: `def quicksort(arr):\n    if len(arr) <= 1:\n        return arr\n    \n    pivot = arr[len(arr) // 2]\n    left = [x for x in arr if x < pivot]\n    middle = [x for x in arr if x == pivot]\n    right = [x for x in arr if x > pivot]\n    \n    return quicksort(left) + middle + quicksort(right)\n\nprint(quicksort([3, 6, 8, 10, 1, 2, 1]))`,\n        react: `import React, { useState } from 'react';\n\nfunction TodoApp() {\n  const [todos, setTodos] = useState([]);\n  const [input, setInput] = useState('');\n\n  const addTodo = () => {\n    if (input.trim()) {\n      setTodos([...todos, { id: Date.now(), text: input, done: false }]);\n      setInput('');\n    }\n  };\n\n  return (\n    <div className=\"todo-app\">\n      <h1>Todo List</h1>\n      <div>\n        <input \n          value={input}\n          onChange={(e) => setInput(e.target.value)}\n          placeholder=\"Add a todo...\"\n        />\n        <button onClick={addTodo}>Add</button>\n      </div>\n      <ul>\n        {todos.map(todo => (\n          <li key={todo.id}>{todo.text}</li>\n        ))}\n      </ul>\n    </div>\n  );\n}`\n    };\n    const handleCodeChange = (newCode)=>{\n        setCode(newCode);\n        if (autoDetectLanguage && newCode.trim()) {\n            const detectedLang = (0,_lib_gemini__WEBPACK_IMPORTED_MODULE_2__.detectLanguage)(newCode);\n            setLanguage(detectedLang);\n        }\n    };\n    const handleGenerateVideo = ()=>{\n        if (!code.trim()) {\n            alert(\"Please enter some code first!\");\n            return;\n        }\n        const videoTitle = title.trim() || `${language.charAt(0).toUpperCase() + language.slice(1)} Code`;\n        const codeData = {\n            code: code.trim(),\n            language,\n            title: videoTitle,\n            explanation: `Generated video for ${language} code`\n        };\n        onGenerateVideo(codeData);\n    };\n    const loadExample = (exampleType)=>{\n        setCode(exampleCodes[exampleType]);\n        setLanguage(exampleType === \"react\" ? \"javascript\" : exampleType);\n        setTitle(exampleType === \"react\" ? \"React Todo App\" : `${exampleType.charAt(0).toUpperCase() + exampleType.slice(1)} Example`);\n    };\n    const pasteFromClipboard = async ()=>{\n        try {\n            const text = await navigator.clipboard.readText();\n            handleCodeChange(text);\n        } catch (err) {\n            console.error(\"Failed to read clipboard:\", err);\n            alert(\"Failed to read from clipboard. Please paste manually.\");\n        }\n    };\n    const clearCode = ()=>{\n        setCode(\"\");\n        setTitle(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-gray-800 rounded-xl p-6 shadow-2xl border border-gray-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-semibold flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code2_Copy_Settings_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                className: \"w-6 h-6 mr-3 text-blue-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, undefined),\n                            \"Code to Video Generator\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: pasteFromClipboard,\n                                className: \"px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded transition-colors\",\n                                title: \"Paste from clipboard\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code2_Copy_Settings_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: clearCode,\n                                className: \"px-3 py-1 bg-red-600 hover:bg-red-700 text-white text-sm rounded transition-colors\",\n                                disabled: !code,\n                                children: \"Clear\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-400 mb-2\",\n                        children: \"Quick Examples:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>loadExample(\"javascript\"),\n                                className: \"px-3 py-1 bg-yellow-600 hover:bg-yellow-700 text-white text-xs rounded transition-colors\",\n                                children: \"Fibonacci (JS)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>loadExample(\"python\"),\n                                className: \"px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-xs rounded transition-colors\",\n                                children: \"Quicksort (Python)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>loadExample(\"react\"),\n                                className: \"px-3 py-1 bg-cyan-600 hover:bg-cyan-700 text-white text-xs rounded transition-colors\",\n                                children: \"Todo App (React)\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                lineNumber: 161,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                        className: \"block text-sm font-medium mb-2 text-gray-300\",\n                        children: \"Your Code\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                        lineNumber: 187,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                        value: code,\n                        onChange: (e)=>handleCodeChange(e.target.value),\n                        placeholder: \"Paste your code here or use the examples above...\",\n                        className: \"w-full h-64 p-4 bg-gray-900 border border-gray-600 rounded-lg font-mono text-sm text-gray-100 placeholder-gray-500 resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                        spellCheck: false\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mt-2 text-xs text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Lines: \",\n                                    code.split(\"\\n\").length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    \"Characters: \",\n                                    code.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                lineNumber: 186,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1 text-gray-300\",\n                                children: \"Language\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: language,\n                                onChange: (e)=>setLanguage(e.target.value),\n                                className: \"w-full p-2 bg-gray-700 border border-gray-600 rounded text-sm text-gray-100\",\n                                children: supportedLanguages.map((lang)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: lang.value,\n                                        children: lang.label\n                                    }, lang.value, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mt-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        id: \"autoDetect\",\n                                        checked: autoDetectLanguage,\n                                        onChange: (e)=>setAutoDetectLanguage(e.target.checked),\n                                        className: \"mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"autoDetect\",\n                                        className: \"text-xs text-gray-400\",\n                                        children: \"Auto-detect\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                        lineNumber: 206,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1 text-gray-300\",\n                                children: \"Video Title\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: title,\n                                onChange: (e)=>setTitle(e.target.value),\n                                placeholder: \"Enter video title...\",\n                                className: \"w-full p-2 bg-gray-700 border border-gray-600 rounded text-sm text-gray-100 placeholder-gray-500\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"block text-sm font-medium mb-1 text-gray-300\",\n                                children: \"Theme\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: theme,\n                                onChange: (e)=>setTheme(e.target.value),\n                                className: \"w-full p-2 bg-gray-700 border border-gray-600 rounded text-sm text-gray-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"dark\",\n                                        children: \"Dark\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                        lineNumber: 259,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"light\",\n                                        children: \"Light\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleGenerateVideo,\n                            disabled: !code.trim() || isGenerating,\n                            className: \"w-full flex items-center justify-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-gray-600 disabled:to-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-all duration-200 font-medium\",\n                            children: isGenerating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"spinner\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Generating...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                        lineNumber: 274,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code2_Copy_Settings_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Generate Video\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                        lineNumber: 265,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                lineNumber: 204,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gray-700 rounded-lg p-3 text-sm text-gray-300\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Code2_Copy_Settings_Wand2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-4 h-4 mr-2 text-blue-400\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                            lineNumber: 289,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Tip:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 11\n                        }, undefined),\n                        \" The video will be 10 seconds long at 1920x1080 resolution. Code will animate line by line with syntax highlighting.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n                lineNumber: 287,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\CodeToVideo.tsx\",\n        lineNumber: 136,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CodeToVideo);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/CodeToVideo.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/VideoControls.tsx":
/*!******************************************!*\
  !*** ./src/components/VideoControls.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Pause_Play_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Pause,Play,RotateCcw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Pause_Play_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Pause,Play,RotateCcw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Pause_Play_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Pause,Play,RotateCcw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Pause_Play_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Pause,Play,RotateCcw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Download_Pause_Play_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Download,Pause,Play,RotateCcw!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst VideoControls = ({ isPlaying, onPlayPause, onRestart, onExport, currentFrame, totalFrames, fps })=>{\n    const formatTime = (frames, fps)=>{\n        const seconds = frames / fps;\n        const minutes = Math.floor(seconds / 60);\n        const remainingSeconds = Math.floor(seconds % 60);\n        return `${minutes}:${remainingSeconds.toString().padStart(2, \"0\")}`;\n    };\n    const progress = currentFrame / totalFrames * 100;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between text-sm text-gray-400 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: formatTime(currentFrame, fps)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: formatTime(totalFrames, fps)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full bg-gray-700 rounded-full h-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-blue-600 h-2 rounded-full transition-all duration-300\",\n                            style: {\n                                width: `${progress}%`\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onPlayPause,\n                                className: \"flex items-center justify-center w-12 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-full transition-colors\",\n                                children: isPlaying ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Pause_Play_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 15\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Pause_Play_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"w-6 h-6 ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onRestart,\n                                className: \"flex items-center justify-center w-10 h-10 bg-gray-600 hover:bg-gray-700 text-white rounded-full transition-colors\",\n                                title: \"Restart\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Pause_Play_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center text-sm text-gray-400 ml-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Pause_Play_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-4 h-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"30 FPS\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-400\",\n                                children: [\n                                    \"Frame \",\n                                    currentFrame,\n                                    \" / \",\n                                    totalFrames\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: onExport,\n                                className: \"flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Download_Pause_Play_RotateCcw_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Export\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-3 gap-4 text-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-700 rounded-lg p-3 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: \"Resolution\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-semibold\",\n                                children: \"1920\\xd71080\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-700 rounded-lg p-3 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: \"Duration\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-semibold\",\n                                children: formatTime(totalFrames, fps)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gray-700 rounded-lg p-3 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400\",\n                                children: \"Format\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"font-semibold\",\n                                children: \"MP4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\components\\\\VideoControls.tsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (VideoControls);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/VideoControls.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useChat.ts":
/*!******************************!*\
  !*** ./src/hooks/useChat.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useChat: () => (/* binding */ useChat)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_gemini__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/gemini */ \"(ssr)/./src/lib/gemini.ts\");\n\n\nconst useChat = ()=>{\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const addMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((message)=>{\n        const newMessage = {\n            ...message,\n            id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n            timestamp: new Date()\n        };\n        setMessages((prev)=>[\n                ...prev,\n                newMessage\n            ]);\n        return newMessage;\n    }, []);\n    const sendMessage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (content)=>{\n        if (!content.trim()) return;\n        setIsLoading(true);\n        setError(null);\n        // Add user message\n        const userMessage = addMessage({\n            role: \"user\",\n            content: content.trim()\n        });\n        try {\n            // Check if this looks like a code generation request\n            const isCodeRequest = /(?:write|create|generate|build|make|code|function|class|component)/i.test(content) || /(?:javascript|python|react|typescript|html|css|java|cpp|rust|go)/i.test(content);\n            let assistantResponse;\n            let codeResponse;\n            if (isCodeRequest) {\n                // Generate code\n                try {\n                    codeResponse = await (0,_lib_gemini__WEBPACK_IMPORTED_MODULE_1__.generateCode)({\n                        prompt: content\n                    });\n                    assistantResponse = `I've generated some ${codeResponse.language} code for you. ${codeResponse.explanation || \"\"}`;\n                } catch (codeError) {\n                    console.warn(\"Code generation failed, falling back to chat:\", codeError);\n                    assistantResponse = await (0,_lib_gemini__WEBPACK_IMPORTED_MODULE_1__.chatWithGemini)(content, messages);\n                }\n            } else {\n                // Regular chat\n                assistantResponse = await (0,_lib_gemini__WEBPACK_IMPORTED_MODULE_1__.chatWithGemini)(content, messages);\n            }\n            // Add assistant message\n            addMessage({\n                role: \"assistant\",\n                content: assistantResponse,\n                codeResponse\n            });\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"An unexpected error occurred\";\n            setError(errorMessage);\n            // Add error message\n            addMessage({\n                role: \"assistant\",\n                content: `Sorry, I encountered an error: ${errorMessage}`\n            });\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        messages,\n        addMessage\n    ]);\n    const generateCodeFromPrompt = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (prompt, language = \"javascript\")=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const response = await (0,_lib_gemini__WEBPACK_IMPORTED_MODULE_1__.generateCode)({\n                prompt,\n                language\n            });\n            // Add messages for the code generation\n            addMessage({\n                role: \"user\",\n                content: `Generate ${language} code: ${prompt}`\n            });\n            addMessage({\n                role: \"assistant\",\n                content: `I've generated ${language} code for your request.`,\n                codeResponse: response\n            });\n            return response;\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"Failed to generate code\";\n            setError(errorMessage);\n            return null;\n        } finally{\n            setIsLoading(false);\n        }\n    }, [\n        addMessage\n    ]);\n    const clearMessages = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setMessages([]);\n        setError(null);\n    }, []);\n    const clearError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setError(null);\n    }, []);\n    return {\n        messages,\n        isLoading,\n        error,\n        sendMessage,\n        generateCodeFromPrompt,\n        clearMessages,\n        clearError\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useChat.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/gemini.ts":
/*!***************************!*\
  !*** ./src/lib/gemini.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   chatWithGemini: () => (/* binding */ chatWithGemini),\n/* harmony export */   detectLanguage: () => (/* binding */ detectLanguage),\n/* harmony export */   explainCode: () => (/* binding */ explainCode),\n/* harmony export */   geminiService: () => (/* binding */ geminiService),\n/* harmony export */   generateCode: () => (/* binding */ generateCode),\n/* harmony export */   improveCode: () => (/* binding */ improveCode)\n/* harmony export */ });\n/* harmony import */ var _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @google/generative-ai */ \"(ssr)/./node_modules/@google/generative-ai/dist/index.mjs\");\n\nclass GeminiService {\n    constructor(){\n        this.genAI = null;\n        this.model = null;\n        this.initialize();\n    }\n    initialize() {\n        const apiKey = \"AIzaSyDNx1rMll9c-OUlAglZuHJDici3TrY9i3w\";\n        if (!apiKey) {\n            console.warn(\"Gemini API key not found. Please set NEXT_PUBLIC_GEMINI_API_KEY in your environment variables.\");\n            return;\n        }\n        try {\n            this.genAI = new _google_generative_ai__WEBPACK_IMPORTED_MODULE_0__.GoogleGenerativeAI(apiKey);\n            this.model = this.genAI.getGenerativeModel({\n                model: \"gemini-2.0-flash-exp\",\n                generationConfig: {\n                    temperature: 0.7,\n                    topP: 0.8,\n                    topK: 40,\n                    maxOutputTokens: 2048\n                }\n            });\n        } catch (error) {\n            console.error(\"Failed to initialize Gemini AI:\", error);\n        }\n    }\n    async generateCode(request) {\n        if (!this.model) {\n            throw new Error(\"Gemini AI not initialized. Please check your API key.\");\n        }\n        try {\n            const { prompt, language = \"javascript\", context = \"\" } = request;\n            const systemPrompt = `You are an expert programmer. Generate clean, well-commented code based on the user's request.\n\nInstructions:\n1. Generate code in ${language} unless the user specifies otherwise\n2. Include helpful comments explaining the logic\n3. Make the code production-ready and follow best practices\n4. If the request is unclear, make reasonable assumptions\n5. Provide a brief title for the code snippet\n\n${context ? `Additional context: ${context}` : \"\"}\n\nFormat your response as JSON with the following structure:\n{\n  \"code\": \"// Your generated code here\",\n  \"language\": \"${language}\",\n  \"title\": \"Brief descriptive title\",\n  \"explanation\": \"Brief explanation of what the code does\"\n}\n\nUser request: ${prompt}`;\n            const result = await this.model.generateContent(systemPrompt);\n            const response = await result.response;\n            const text = response.text();\n            // Try to parse JSON response\n            try {\n                const jsonMatch = text.match(/\\{[\\s\\S]*\\}/);\n                if (jsonMatch) {\n                    const parsed = JSON.parse(jsonMatch[0]);\n                    return {\n                        code: parsed.code || text,\n                        language: parsed.language || language,\n                        title: parsed.title || \"Generated Code\",\n                        explanation: parsed.explanation || \"\"\n                    };\n                }\n            } catch (parseError) {\n                console.warn(\"Failed to parse JSON response, using raw text\");\n            }\n            // Fallback: extract code from markdown blocks or use raw text\n            const codeMatch = text.match(/```(?:\\w+)?\\n?([\\s\\S]*?)```/);\n            const extractedCode = codeMatch ? codeMatch[1] : text;\n            return {\n                code: extractedCode.trim(),\n                language,\n                title: \"Generated Code\",\n                explanation: \"Code generated by Gemini 2.0 Flash\"\n            };\n        } catch (error) {\n            console.error(\"Error generating code:\", error);\n            throw new Error(`Failed to generate code: ${error instanceof Error ? error.message : \"Unknown error\"}`);\n        }\n    }\n    async chatWithGemini(message, conversationHistory = []) {\n        if (!this.model) {\n            throw new Error(\"Gemini AI not initialized. Please check your API key.\");\n        }\n        try {\n            // Build conversation context\n            const context = conversationHistory.slice(-10) // Keep last 10 messages for context\n            .map((msg)=>`${msg.role}: ${msg.content}`).join(\"\\n\");\n            const prompt = context ? `Previous conversation:\\n${context}\\n\\nUser: ${message}\\nAssistant:` : `User: ${message}\\nAssistant:`;\n            const result = await this.model.generateContent(prompt);\n            const response = await result.response;\n            return response.text();\n        } catch (error) {\n            console.error(\"Error in chat:\", error);\n            throw new Error(`Chat failed: ${error instanceof Error ? error.message : \"Unknown error\"}`);\n        }\n    }\n    async improveCode(code, improvement) {\n        const prompt = `Improve the following code based on this request: \"${improvement}\"\n\nCurrent code:\n\\`\\`\\`\n${code}\n\\`\\`\\`\n\nPlease provide the improved version with explanations of the changes made.`;\n        return this.generateCode({\n            prompt\n        });\n    }\n    async explainCode(code) {\n        if (!this.model) {\n            throw new Error(\"Gemini AI not initialized. Please check your API key.\");\n        }\n        try {\n            const prompt = `Explain the following code in simple terms:\n\n\\`\\`\\`\n${code}\n\\`\\`\\`\n\nProvide a clear explanation of what this code does, how it works, and any important concepts it demonstrates.`;\n            const result = await this.model.generateContent(prompt);\n            const response = await result.response;\n            return response.text();\n        } catch (error) {\n            console.error(\"Error explaining code:\", error);\n            throw new Error(`Failed to explain code: ${error instanceof Error ? error.message : \"Unknown error\"}`);\n        }\n    }\n    detectLanguage(code) {\n        // Simple language detection based on syntax patterns\n        if (code.includes(\"def \") || code.includes(\"import \") || code.includes(\"print(\")) {\n            return \"python\";\n        }\n        if (code.includes(\"function \") || code.includes(\"const \") || code.includes(\"console.log\")) {\n            return \"javascript\";\n        }\n        if (code.includes(\"public class \") || code.includes(\"System.out.println\")) {\n            return \"java\";\n        }\n        if (code.includes(\"#include\") || code.includes(\"std::\")) {\n            return \"cpp\";\n        }\n        if (code.includes(\"fn \") || code.includes(\"let mut \")) {\n            return \"rust\";\n        }\n        if (code.includes(\"func \") || code.includes(\"package main\")) {\n            return \"go\";\n        }\n        return \"javascript\"; // Default fallback\n    }\n    isInitialized() {\n        return this.model !== null;\n    }\n}\n// Export singleton instance\nconst geminiService = new GeminiService();\n// Export utility functions\nconst generateCode = (request)=>geminiService.generateCode(request);\nconst chatWithGemini = (message, history)=>geminiService.chatWithGemini(message, history);\nconst improveCode = (code, improvement)=>geminiService.improveCode(code, improvement);\nconst explainCode = (code)=>geminiService.explainCode(code);\nconst detectLanguage = (code)=>geminiService.detectLanguage(code);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/gemini.ts\n");

/***/ }),

/***/ "(ssr)/./src/remotion/CodeVideo.tsx":
/*!************************************!*\
  !*** ./src/remotion/CodeVideo.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CodeVideo: () => (/* binding */ CodeVideo)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var remotion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! remotion */ \"(ssr)/./node_modules/remotion/dist/esm/index.mjs\");\n\n\n\nconst CodeVideo = ({ code, language, title = \"Generated Code\", theme = \"dark\" })=>{\n    const frame = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.useCurrentFrame)();\n    const { fps, durationInFrames } = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.useVideoConfig)();\n    // Animation timings\n    const titleDelay = 0;\n    const codeDelay = 30;\n    const lineDelay = 5;\n    // Title animation\n    const titleProgress = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.spring)({\n        frame: frame - titleDelay,\n        fps,\n        config: {\n            damping: 100,\n            stiffness: 200,\n            mass: 0.5\n        }\n    });\n    const titleOpacity = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.interpolate)(titleProgress, [\n        0,\n        1\n    ], [\n        0,\n        1\n    ]);\n    const titleScale = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.interpolate)(titleProgress, [\n        0,\n        1\n    ], [\n        0.8,\n        1\n    ]);\n    // Code lines animation\n    const codeLines = code.split(\"\\n\");\n    const totalLines = codeLines.length;\n    const getLineProgress = (lineIndex)=>{\n        const lineStartFrame = codeDelay + lineIndex * lineDelay;\n        return (0,remotion__WEBPACK_IMPORTED_MODULE_2__.spring)({\n            frame: frame - lineStartFrame,\n            fps,\n            config: {\n                damping: 100,\n                stiffness: 300,\n                mass: 0.3\n            }\n        });\n    };\n    // Background gradient animation\n    const backgroundProgress = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.interpolate)(frame, [\n        0,\n        durationInFrames\n    ], [\n        0,\n        1\n    ]);\n    const gradientRotation = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.interpolate)(backgroundProgress, [\n        0,\n        1\n    ], [\n        0,\n        360\n    ]);\n    // Syntax highlighting colors\n    const getTokenColor = (token, theme)=>{\n        const darkColors = {\n            keyword: \"#ff79c6\",\n            string: \"#f1fa8c\",\n            number: \"#bd93f9\",\n            comment: \"#6272a4\",\n            function: \"#50fa7b\",\n            variable: \"#8be9fd\",\n            default: \"#f8f8f2\"\n        };\n        const lightColors = {\n            keyword: \"#d73a49\",\n            string: \"#032f62\",\n            number: \"#005cc5\",\n            comment: \"#6a737d\",\n            function: \"#6f42c1\",\n            variable: \"#e36209\",\n            default: \"#24292e\"\n        };\n        const colors = theme === \"dark\" ? darkColors : lightColors;\n        // Simple token detection\n        if (/^(function|const|let|var|if|else|for|while|return|class|import|export)$/.test(token)) {\n            return colors.keyword;\n        }\n        if (/^['\"`].*['\"`]$/.test(token)) {\n            return colors.string;\n        }\n        if (/^\\d+$/.test(token)) {\n            return colors.number;\n        }\n        if (/^\\/\\//.test(token)) {\n            return colors.comment;\n        }\n        if (/^[a-zA-Z_$][a-zA-Z0-9_$]*\\s*\\(/.test(token)) {\n            return colors.function;\n        }\n        return colors.default;\n    };\n    const renderCodeLine = (line, lineIndex)=>{\n        const lineProgress = getLineProgress(lineIndex);\n        const lineOpacity = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.interpolate)(lineProgress, [\n            0,\n            1\n        ], [\n            0,\n            1\n        ]);\n        const lineTranslateX = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.interpolate)(lineProgress, [\n            0,\n            1\n        ], [\n            -50,\n            0\n        ]);\n        // Simple tokenization\n        const tokens = line.split(/(\\s+|[(){}[\\];,.])/);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center font-mono text-lg leading-relaxed\",\n            style: {\n                opacity: lineOpacity,\n                transform: `translateX(${lineTranslateX}px)`\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-gray-500 w-12 text-right mr-4 select-none\",\n                    children: lineIndex + 1\n                }, void 0, false, {\n                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1\",\n                    children: tokens.map((token, tokenIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            style: {\n                                color: getTokenColor(token.trim(), theme)\n                            },\n                            children: token\n                        }, tokenIndex, false, {\n                            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, lineIndex, true, {\n            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n            lineNumber: 128,\n            columnNumber: 7\n        }, undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(remotion__WEBPACK_IMPORTED_MODULE_2__.AbsoluteFill, {\n        className: `${theme === \"dark\" ? \"bg-gray-900\" : \"bg-gray-50\"} flex flex-col items-center justify-center p-16`,\n        style: {\n            background: theme === \"dark\" ? `linear-gradient(${gradientRotation}deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%)` : `linear-gradient(${gradientRotation}deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%)`\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 overflow-hidden\",\n                children: [\n                    ...Array(20)\n                ].map((_, i)=>{\n                    const delay = i * 10;\n                    const progress = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.spring)({\n                        frame: frame - delay,\n                        fps,\n                        config: {\n                            damping: 100,\n                            stiffness: 200\n                        }\n                    });\n                    const opacity = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.interpolate)(progress, [\n                        0,\n                        1\n                    ], [\n                        0,\n                        0.1\n                    ]);\n                    const scale = (0,remotion__WEBPACK_IMPORTED_MODULE_2__.interpolate)(progress, [\n                        0,\n                        1\n                    ], [\n                        0,\n                        1\n                    ]);\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `absolute w-2 h-2 ${theme === \"dark\" ? \"bg-blue-400\" : \"bg-blue-600\"} rounded-full`,\n                        style: {\n                            left: `${i * 47 % 100}%`,\n                            top: `${i * 31 % 100}%`,\n                            opacity,\n                            transform: `scale(${scale})`\n                        }\n                    }, i, false, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 13\n                    }, undefined);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                lineNumber: 167,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-12 text-center\",\n                style: {\n                    opacity: titleOpacity,\n                    transform: `scale(${titleScale})`\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: `text-5xl font-bold mb-4 ${theme === \"dark\" ? \"text-white\" : \"text-gray-900\"}`,\n                        children: title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: `text-xl ${theme === \"dark\" ? \"text-blue-400\" : \"text-blue-600\"} font-mono`,\n                        children: language.toUpperCase()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `${theme === \"dark\" ? \"bg-gray-800 border-gray-700\" : \"bg-white border-gray-200\"} border-2 rounded-2xl p-8 shadow-2xl max-w-4xl w-full`,\n                style: {\n                    backdropFilter: \"blur(10px)\",\n                    backgroundColor: theme === \"dark\" ? \"rgba(31, 41, 55, 0.9)\" : \"rgba(255, 255, 255, 0.9)\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mb-6 pb-4 border-b border-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-red-500 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-yellow-500 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-3 h-3 bg-green-500 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `ml-4 text-sm ${theme === \"dark\" ? \"text-gray-400\" : \"text-gray-600\"}`,\n                                children: [\n                                    language,\n                                    \".\",\n                                    language === \"javascript\" ? \"js\" : language === \"python\" ? \"py\" : \"txt\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2\",\n                        children: codeLines.map((line, index)=>renderCodeLine(line, index))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `mt-8 text-center ${theme === \"dark\" ? \"text-gray-400\" : \"text-gray-600\"}`,\n                style: {\n                    opacity: (0,remotion__WEBPACK_IMPORTED_MODULE_2__.interpolate)(frame, [\n                        durationInFrames - 60,\n                        durationInFrames\n                    ], [\n                        0,\n                        1\n                    ])\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg\",\n                    children: \"Generated with Gemini 2.0 Flash & Remotion\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n                lineNumber: 256,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\remotion\\\\CodeVideo.tsx\",\n        lineNumber: 156,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/remotion/CodeVideo.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8bf296b95d5c\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcmVtb3Rpb24tZ2VtaW5pLXZpZGVvLWdlbmVyYXRvci8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MDAwMiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjhiZjI5NmI5NWQ1Y1wiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\n\nconst metadata = {\n    title: \"Remotion + Gemini Code Video Generator\",\n    description: \"Generate animated code videos using Gemini 2.0 Flash and Remotion\",\n    keywords: [\n        \"remotion\",\n        \"gemini\",\n        \"code\",\n        \"video\",\n        \"generation\",\n        \"ai\"\n    ],\n    authors: [\n        {\n            name: \"Code Video Generator\"\n        }\n    ]\n};\nconst viewport = {\n    width: \"device-width\",\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_2___default().className)} bg-gray-900 text-gray-100 antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900\",\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 27,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 26,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\video\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 25,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\PiyushWorkspace\code\Onest\video\src\app\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/remotion","vendor-chunks/@remotion","vendor-chunks/@google"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CPiyushWorkspace%5Ccode%5COnest%5Cvideo%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CPiyushWorkspace%5Ccode%5COnest%5Cvideo&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();